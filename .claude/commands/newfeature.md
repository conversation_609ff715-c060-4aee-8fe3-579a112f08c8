You must follow this workflow perfectly
1. Start with the pair-programmer agent to review this
2. Confirm with the user which approach to take this may take several conversations
3. Proceed to implement the solution, this can be done with multiple general-purpose agents
4. Once implementation is done use the code-reviewer agent to review the code
5. Use general purpose agents to implement any required feedback, if the feedback is minor then you can ask the user for confirmation
6. Show the results to the user.
