# Raleon Tools Documentation

This document provides a comprehensive overview of all tools (sub-agents) available in the Raleon Web App, including both server-side and client-side implementations, with special emphasis on tools that use external LLMs.

## Overview

Raleon Web App implements a sophisticated tool system that combines server-side AI-powered tools with client-side tag-based handlers for streaming UI updates. The architecture supports both direct LLM integrations and specialized sub-agents for various marketing and ecommerce tasks.

## Architecture

```mermaid
graph TB
    subgraph "Client-Side (Vue 3)"
        UI[Chat Interface]
        Parser[Message Parser Service]
        StreamHandler[Chat Streaming Composable]
        ToolRegistry[Tool Handler Registry]
        
        subgraph "Tag-Based Tool Handlers"
            BriefHandler[Brief Tool Handler]
            EmailHandler[Email Tool Handler]
            PlanHandler[Plan Tool Handler]
            ImageHandler[Image Tool Handler]
            ImageEditHandler[Image Edit Tool Handler]
            ImageTextEditHandler[Image Text Edit Tool Handler]
            MultiImageHandler[Multi Image Tool Handler]
            MemoryHandler[Memory Tool Handler]
            AnalyticsHandler[Analytics Tool Handler]
            SwitchModeHandler[Switch Mode Tool Handler]
        end
    end

    subgraph "Server-Side (LoopBack 4)"
        ChatController[Chat Controller]
        ToolExecutor[Tool Executor Service]
        
        subgraph "AI/LLM Services"
            OpenAIService[OpenAI Service]
            ClaudeService[Claude Service]
            AnthropicTarget[Anthropic Target Service]
            OpenAITarget[OpenAI Target Service]
            OpenRouterTarget[Open Router Target Service]
            LLMRouter[LLM Router Service]
        end
        
        subgraph "Data & Integration Tools"
            DataLookup[data_lookup ⭐]
            MetricLookup[metric_lookup]
            ProductLookup[product_lookup]
            BestSellers[best_sellers]
            ImageLookup[image_lookup]
            ImageEdit[image_edit ⭐]
            ImageInfo[image_info ⭐]
        end
        
        subgraph "External APIs"
            ShopifyAPI[Shopify API]
            AWSAthena[AWS Athena]
            Flux1API[Flux1 Kontext API]
            S3Storage[AWS S3]
        end
    end

    UI --> Parser
    Parser --> StreamHandler
    StreamHandler --> ToolRegistry
    ToolRegistry --> BriefHandler
    ToolRegistry --> EmailHandler
    ToolRegistry --> PlanHandler
    ToolRegistry --> ImageHandler
    ToolRegistry --> ImageEditHandler
    ToolRegistry --> ImageTextEditHandler
    ToolRegistry --> MultiImageHandler
    ToolRegistry --> MemoryHandler
    ToolRegistry --> AnalyticsHandler
    ToolRegistry --> SwitchModeHandler

    UI --> ChatController
    ChatController --> ToolExecutor
    ToolExecutor --> DataLookup
    ToolExecutor --> MetricLookup
    ToolExecutor --> ProductLookup
    ToolExecutor --> BestSellers
    ToolExecutor --> ImageLookup
    ToolExecutor --> ImageEdit
    ToolExecutor --> ImageInfo

    DataLookup --> LLMRouter
    MetricLookup --> AWSAthena
    ProductLookup --> ShopifyAPI
    BestSellers --> ShopifyAPI
    ImageLookup --> S3Storage
    ImageEdit --> Flux1API
    ImageEdit --> OpenAIService
    ImageInfo --> OpenAIService

    ChatController --> LLMRouter
    LLMRouter --> OpenAITarget
    LLMRouter --> AnthropicTarget
    LLMRouter --> OpenRouterTarget
    
    OpenAITarget --> OpenAIService
    AnthropicTarget --> ClaudeService
    OpenRouterTarget --> OpenAIService

    ImageEdit --> S3Storage
```

## Server-Side Tools (Sub-Agents)

### 1. Data & Analytics Tools

#### `data_lookup` Tool ⭐ (LLM-Powered)
- **Purpose**: Query data from AWS Athena tables using natural language
- **LLM Integration**: **Claude 3.7 Sonnet** via LLM Router for natural language to SQL conversion
- **Key Features**:
  - Natural language query processing with prompt templates
  - Email campaign analytics (open rates, revenue, click rates)
  - Subject line performance analysis
  - Order information querying
  - Organization-scoped data access with validation
  - Dual LLM call pattern for security (fallback if orgId not included)
- **Security**: Built-in orgId validation, SQL injection protection, prompt template management
- **File Location**: `src/controllers/chat.controller.ts:421-461`

#### `metric_lookup` Tool
- **Purpose**: Retrieve metrics from pre-computed database tables
- **Integration**: Direct database queries via EcommerceMetricController
- **Key Features**:
  - Pre-computed metric retrieval (no LLM processing)
  - Caching with NodeCache (5-minute TTL)
  - Organization-scoped metric access
  - Latest run date filtering
- **File Location**: `src/controllers/chat.controller.ts:361-420`

#### `product_lookup` Tool
- **Purpose**: Search and retrieve product information from Shopify
- **Integration**: Direct Shopify Admin API integration
- **Key Features**:
  - Fuzzy search using Fuse.js
  - Product filtering by title, type, tags, vendor
  - Inventory and pricing information
  - Caching with NodeCache (5-minute TTL)
- **File Location**: `src/services/tool-executor.service.ts:59`

#### `best_sellers` Tool
- **Purpose**: Retrieve top-performing products by sales metrics
- **Integration**: Shopify API with simulated analytics
- **Key Features**:
  - Time-based analysis (30d, 60d, etc.)
  - Revenue and sales count metrics
  - Active product filtering
- **File Location**: `src/services/tool-executor.service.ts:155`

### 2. Image Processing Tools (LLM-Powered)

#### `image_lookup` Tool
- **Purpose**: Search brand images using AI tags and descriptions
- **Key Features**:
  - AI tag-based filtering
  - Description keyword matching
  - Aspect ratio calculation
  - Organization-scoped image access
- **File Location**: `src/services/tool-executor.service.ts:229`

#### `image_edit` Tool ⭐ (Multi-AI)
- **Purpose**: AI-powered image editing with text and object modifications
- **AI Service Integrations**: 
  - **Primary**: **Flux1 Kontext API** (BFL API) for advanced image editing
  - **Fallback**: **OpenAI DALL-E** for image editing
  - **Image Processing**: **Sharp** for aspect ratio detection and metadata
- **Key Features**:
  - Text addition/replacement with styling parsing
  - Object modifications and style changes
  - Automatic aspect ratio detection (1:1, 16:9, 9:16)
  - Generates 3 variations per edit
  - S3 storage integration for permanent URLs
  - Dual implementation paths (Flux1 vs OpenAI)
- **API Keys**: Uses BFL_API_KEY for Flux1 Kontext Pro API
- **File Locations**: 
  - Tool implementation: `src/services/tool-executor.service.ts:341`
  - Flux1 service: `src/services/chat/chat.service.ts:editImageFlux1`
  - Chat controller: `src/controllers/chat.controller.ts:694`

#### `image_info` Tool ⭐ (LLM-Powered)
- **Purpose**: Extract and analyze text content from images using AI vision
- **LLM Integration**: **OpenAI GPT-4o-mini** with vision capabilities
- **Key Features**:
  - Structured text extraction (main text, buttons, navigation)
  - Content type identification
  - Ignores watermarks and small text
  - JSON-formatted response with categorization
- **Use Cases**:
  - Content audits and accessibility reviews
  - Competitive analysis of screenshots
  - Marketing material text extraction
- **File Location**: `src/services/tool-executor.service.ts:386`

### 3. LLM Service Layer

#### OpenAI Service ⭐
- **Models Supported**:
  - **O3-mini**: Advanced reasoning tasks
  - **GPT-4o-mini**: Vision and image analysis
  - **DALL-E**: Image generation and editing
- **Key Methods**:
  - `getO3Completion()`: JSON-formatted responses
  - `getImageDescription()`: Image analysis with vision
  - `getImageInfo()`: Structured text extraction from images
- **File Location**: `src/services/open-ai.service.ts`

#### Claude Service ⭐
- **Integration**: Anthropic Claude API
- **Purpose**: Alternative LLM for chat and content generation
- **File Location**: `src/services/claude.service.ts`

#### LLM Router Service
- **Purpose**: Route requests to appropriate LLM services
- **Supported Targets**:
  - OpenAI Target Service
  - Anthropic Target Service  
  - Open Router Target Service
- **File Location**: `src/services/chat/llm-router.service.ts`

## Client-Side Tool Handlers

### Tag-Based Processing System

The client-side implements a sophisticated tag-based tool system that processes streaming AI responses and renders specialized UI components.

#### Core Handler Classes

All handlers extend `BaseToolHandler` and implement the `ToolHandler` interface:

```typescript
interface ToolHandler {
  readonly tag: string;
  onStart(placeholderId: string, initialContent?: string): void;
  onContent(content: string): void;
  onEnd(): void;
}
```

### 1. Content Generation Handlers

#### Brief Tool Handler
- **Tag**: `brief`
- **Purpose**: Email brief generation with subject, preview, and content
- **Features**: JSON parsing, streaming updates, artifact management
- **File Location**: `public/client/services/tools/BriefToolHandler.ts`

#### Email Tool Handler
- **Tag**: `email`  
- **Purpose**: Email design generation with component JSON
- **Features**: Unlayer integration, template processing
- **File Location**: `public/client/services/tools/EmailToolHandler.ts`

#### Plan Tool Handler
- **Tag**: `plan`
- **Purpose**: Marketing plan generation with structured campaign data
- **Features**: Campaign structure, timeline planning
- **File Location**: `public/client/services/tools/PlanToolHandler.ts`

### 2. Image Display Handlers

#### Image Tool Handler
- **Tag**: `image`
- **Purpose**: Single image display from AI responses
- **Features**: URL extraction, fallback parsing, error handling
- **File Location**: `public/client/services/tools/ImageToolHandler.ts`

#### Multi Image Tool Handler
- **Tag**: `multiimage`
- **Purpose**: Multiple image gallery display
- **Features**: JSON array processing, gallery rendering
- **File Location**: `public/client/services/tools/MultiImageToolHandler.ts`

#### Image Edit Tool Handler
- **Tag**: `ie` (Image Edit)
- **Purpose**: Handle image editing parameters and results
- **Features**: Edit parameter processing, result display
- **File Location**: `public/client/services/tools/ImageEditToolHandler.ts`

#### Image Text Edit Tool Handler
- **Tag**: `ite` (Image Text Edit)
- **Purpose**: Specialized text editing on images
- **Features**: Text replacement tracking, preview handling
- **File Location**: `public/client/services/tools/ImageTextEditToolHandler.ts`

### 3. Utility Handlers

#### Memory Tool Handler
- **Tag**: `memory`
- **Purpose**: Brand memory storage and retrieval
- **Features**: Context persistence, brand guideline storage
- **File Location**: `public/client/services/tools/MemoryToolHandler.ts`

#### Analytics Tool Handler
- **Tag**: `analytics`
- **Purpose**: Analytics data display and visualization
- **Features**: Chart integration, metric formatting
- **File Location**: `public/client/services/tools/AnalyticsToolHandler.ts`

#### Switch Mode Tool Handler
- **Tag**: `switch_mode`
- **Purpose**: Navigation and mode switching
- **Features**: Route navigation, state transitions
- **File Location**: `public/client/services/tools/SwitchModeToolHandler.ts`

## Tool Integration Patterns

### 1. Server-Side Tool Execution

```typescript
// Tool executor pattern
getToolExecutors(): {[functionName: string]: (args: any) => Promise<any>} {
  return {
    'tool_name': async (params: any): Promise<any> => {
      // Tool implementation
      // LLM integration if needed
      // Return structured response
    }
  };
}
```

### 2. Client-Side Tag Processing

```typescript
// Tag-based handler pattern
export class ToolHandler extends BaseToolHandler {
  public readonly tag = 'tool_tag';
  
  onStart(placeholderId: string): void {
    // Initialize streaming
  }
  
  onContent(contentChunk: string): void {
    // Process streaming content
  }
  
  onEnd(): void {
    // Finalize and emit results
  }
}
```

### 3. LLM Integration Pattern

```typescript
// Multi-LLM tool pattern (image_edit example)
private async editImageFlux1(params: any): Promise<any> {
  // Primary: Flux1 Kontext API
  const result = await this.chatService.editImageFlux1(prompt, imageUrl, aspectRatio);
  // Fallback: OpenAI if Flux1 fails
}

private async editImageOpenAI(params: any): Promise<any> {
  // Fallback: OpenAI DALL-E
  const result = await this.chatService.editImage(prompt, imageDataUrls);
}
```

## LLM-Powered Tools Summary

### Tools Using External LLMs/AI Services:

1. **`data_lookup`** ⭐ (LLM-Powered)
   - **LLM**: **Claude 3.7 Sonnet** via LLM Router
   - **Purpose**: Natural language to SQL query conversion
   - **Pattern**: Dual LLM call with security validation

2. **`image_edit`** ⭐ (Multi-AI Service)
   - **Primary AI**: **Flux1 Kontext API** (BFL API) 
   - **Fallback AI**: **OpenAI DALL-E**
   - **Supporting**: **Sharp** library for image processing
   - **Purpose**: Advanced AI image editing and modification

3. **`image_info`** ⭐ (LLM-Powered)
   - **LLM**: **OpenAI GPT-4o-mini** with vision capabilities
   - **Purpose**: Structured text extraction and image analysis

4. **Chat System** ⭐ (Multi-LLM)
   - **Models**: Multiple LLM support via Router
     - **OpenAI** (GPT-4, O3-mini, DALL-E)
     - **Anthropic Claude** (3.7 Sonnet)
     - **Open Router** models
   - **Purpose**: Conversational AI and tool orchestration

### Tools NOT Using LLMs:

- **`metric_lookup`**: Direct database queries (no LLM processing)
- **`product_lookup`**: Direct Shopify API calls with fuzzy search
- **`best_sellers`**: Direct database queries with sorting
- **`image_lookup`**: Direct database image search (no AI processing)

## Development Guidelines

### Adding New Server-Side Tools

1. Add tool function to `ToolExecutorService.getToolExecutors()`
2. Implement tool logic with appropriate LLM integration
3. Add tool documentation to prompt templates
4. Update chat controller integration

### Adding New Client-Side Handlers

1. Create handler class extending `BaseToolHandler`
2. Register in `tools/index.ts`
3. Update message parser for tag recognition
4. Add UI components for rendering
5. Update streaming composable for state management

### LLM Integration Best Practices

1. **Multiple Model Support**: Implement primary and fallback LLM options
2. **Error Handling**: Graceful degradation when LLM services fail
3. **Response Validation**: Parse and validate LLM responses
4. **Cost Optimization**: Use appropriate models for specific tasks
5. **Security**: Validate inputs and sanitize LLM outputs

## Testing and Monitoring

### Tool Testing
- Unit tests for individual tool functions
- Integration tests for LLM service connectivity
- End-to-end tests for client-server tool coordination

### Performance Monitoring
- LLM response times and success rates
- Tool execution metrics
- Client-side streaming performance
- Cache effectiveness for data tools

## Security Considerations

### Server-Side Security
- Organization ID validation and scoping
- SQL injection prevention in data tools
- API key management for external services
- Input sanitization for LLM prompts

### Client-Side Security
- XSS prevention in dynamic content rendering
- Secure image URL handling
- Tool handler input validation

This documentation serves as a comprehensive guide to understanding and working with Raleon's sophisticated tool ecosystem, highlighting the integration of multiple LLMs and the hybrid client-server architecture that enables powerful AI-driven marketing automation capabilities.