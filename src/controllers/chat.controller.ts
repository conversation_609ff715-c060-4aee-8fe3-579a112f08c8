import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {Filter, repository} from '@loopback/repository';
import {
	api,
	get,
	param,
        post,
        patch,
        del,
        Request,
        requestBody,
        Response,
        RestBindings,
        HttpErrors,
} from '@loopback/rest';
import {guardStrategy, OrgGuardPropertyStrategy, injectUserOrgId, injectUserId, modelForGuard, restrictReadsWithGuard, skipGuardCheck, modelIdForGuard} from '../interceptors';
import {Conversation, PlannerCampaignImage, Message, MessageRole, MessageStatus, Task} from '../models';
import {ConversationRepository, MessageRepository, PlannerCampaignImageRepository, PromptTemplateRepository, ImageRepository, OrganizationRepository, TaskRepository} from '../repositories';
import {ChatService, basicAuthorization, MessageQuotaService} from '../services';
import {RouterParams, ToolDefinition, CompletionMessage} from '../services/chat/types';
import {LLMRouterService} from '../services/chat/llm-router.service';
import {PromptContextService} from '../services/prompt/prompt-context.service';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {MemcachedLockService} from '../services/mem-cached.service';
import { AdminUiController } from './admin-ui.controller';
import NodeCache from 'node-cache';
import {EcommerceMetricController} from './ecommerce-metric.controller';
import {getURL} from '../utils/utils';
import {calculateAspectRatio} from '../utils/aspectRatioUtils';
import Fuse from 'fuse.js';
const fetch = require('node-fetch')

let DATA_API: string = process.env.ECOMMERCE_AWS_URL! || "nq2mcp0pfh.execute-api.us-east-1.amazonaws.com";

// Message content types
interface ImageUrlContent {
  type: 'image_url';
  image_url: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
}

interface TextContent {
  type: 'text';
  text: string;
}

type MessageContent = TextContent | ImageUrlContent;

// Request types
interface StartConversationRequest {
  message: string | MessageContent[];
  promptTemplateId?: number;
  campaignId?: number;
  taskId?: number;
  stream?: boolean;
  tools?: string[]; // Optional array of tool names to enable
}

interface ContinueConversationRequest {
  message: string | MessageContent[];
  stream?: boolean;
  updateSystemPrompt?: boolean;
  tools?: string[]; // Optional array of tool names to enable
}

interface LLMMetadata {
  system?: string;
  provider?: string;
  model?: string;
  generatedImages?: Array<{url: string; revised_prompt?: string}>;
  editedImages?: Array<{url: string}>;
  [key: string]: unknown;
}

interface MessageWithMetadata {
  id?: number;
  content?: string;
  llmMetadata?: unknown;
  toolCalls?: string;
  toolResults?: string;
}

@api({basePath: '/api/v1'})
@authenticate('jwt')
@guardStrategy(new OrgGuardPropertyStrategy<Conversation>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: ConversationRepository
}))
export class ChatController {
        // Initialize NodeCache for product, metric, and data caching (TTL: 5 minutes, max 1000 keys)
        private readonly productCache = new NodeCache({
                stdTTL: 300,
                checkperiod: 60,
                maxKeys: 1000,
                deleteOnExpire: true,
                useClones: false // Better memory performance
        });
        private readonly metricCache = new NodeCache({
                stdTTL: 300,
                checkperiod: 60,
                maxKeys: 1000,
                deleteOnExpire: true,
                useClones: false // Better memory performance
        });

	// Available tools that can be enabled via the API
	private readonly availableTools: Record<string, any> = {
		//TODO add the metric search tool back in Email prompt template brainstorm mode
		// 'semantic_search': {
		// 	type: 'function',
		// 	function: {
		// 		name: 'semantic_search',
		// 		description: 'Run a natural language search for relevant code or documentation comments from the user\'s current workspace.',
		// 		parameters: {
		// 			type: 'object',
		// 			properties: {
		// 				query: {
		// 					type: 'string',
		// 					description: 'The query to search the codebase for. Should contain all relevant context.'
		// 				}
		// 			},
		// 			required: ['query']
		// 		}
		// 	}
		// },
		'product_lookup': {
			type: 'function',
			function: {
				name: 'product_lookup',
				description: 'Look up product information from the store catalog including details such as name, price, inventory, description, and images, can return more than one product',
				parameters: {
					type: 'object',
					properties: {
						query: {
							type: 'string',
							description: 'The name of the product to search for'
						}
					},
					required: ['query']
				}
			}
		},
		'metric_lookup': {
				type: 'function',
				function: {
						name: 'metric_lookup',
						description: 'Look up metric information from athena, metrics include all relevant data included in the table for the most recent rundate',
						parameters: {
								type: 'object',
								properties: {
										query: {
												type: 'string',
												description: 'The name of metric'
										}
								},
								required: ['query']
						}
				}
		},
		'data_lookup': {
				type: 'function',
				function: {
						name: 'data_lookup',
						description: 'Execute an Athena query against the data warehouse and return the results',
						parameters: {
								type: 'object',
								properties: {
										query: {
												type: 'string',
												description: 'SQL query to run in Athena'
										}
								},
								required: ['query']
						}
				}
		},
		'best_sellers': {
			type: 'function',
			function: {
				name: 'best_sellers',
				description: 'Get best selling products based on timeframe with sales volume and revenue data. Shows actual sales count and revenue for each product.',
				parameters: {
					type: 'object',
					properties: {
						timeframe: {
							type: 'string',
							description: 'Time period for analysis: "7d" (last 7 days), "30d" (last 30 days), "90d" (last 90 days), "1y" (last year), or "all" (all time)',
							enum: ['7d', '30d', '90d', '1y', 'all']
						},
						limit: {
							type: 'number',
							description: 'Maximum number of products to return (default: 10)',
							minimum: 1,
							maximum: 50
						}
					},
					required: ['timeframe']
				}
			}
		},
		'image_lookup': {
			type: 'function',
			function: {
				name: 'image_lookup',
				description: 'Search for brand images by AI tag and/or description. Only returns images that have been tagged for AI use. Use {BRAND_IMAGE_TAGS} to see available tags first.',
				parameters: {
					type: 'object',
					properties: {
						tag: {
							type: 'string',
							description: 'Filter by specific image AI tag (e.g., "product", "lifestyle", "logo")'
						},
						description: {
							type: 'string',
							description: 'Search for images matching this description or containing these keywords'
						},
						limit: {
							type: 'number',
							description: 'Maximum number of images to return (default: 10)',
							minimum: 1,
							maximum: 20
						}
					}
				}
			}
		},
		'image_edit': {
			type: 'function',
			function: {
				name: 'image_edit',
				description: 'Edit an existing image to add or modify text, objects, or general elements. Uses Flux1 Kontext Pro for high-quality image editing. Automatically detects the image aspect ratio and generates variations. Useful for customizing promotional images, adding sale text, updating product information, or modifying visual elements like changing objects, colors, or styles.',
				parameters: {
					type: 'object',
					properties: {
						image_url: {
							type: 'string',
							description: 'URL of the image to edit'
						},
						new_text: {
							type: 'string',
							description: 'Text to add or replace on the image. Simple text works best with Flux1.'
						},
						text_to_replace: {
							type: 'string',
							description: 'Optional existing text on the image to replace. If not provided, the AI will intelligently place the new text'
						},
						modification_description: {
							type: 'string',
							description: 'Description of non-text modifications to make. Examples: "change the dog to a golden retriever", "make the background blue", "add sunglasses to the person", "change the car color to red"'
						}
					},
					required: ['image_url']
				}
			}
		},
		'image_info': {
			type: 'function',
			function: {
				name: 'image_info',
				description: 'Analyze an image and extract readable text content, focusing on larger text, buttons, and navigation elements while ignoring small product labels and watermarks. Uses GPT-4o-mini vision capabilities to provide structured text extraction.',
				parameters: {
					type: 'object',
					properties: {
						url: {
							type: 'string',
							description: 'URL of the image to analyze for text content'
						}
					},
					required: ['url']
				}
			}
		}
    };

	// Tool executor functions
	private readonly toolExecutors: {
		[functionName: string]: (args: any) => Promise<any>;
	} = {
		'semantic_search': async (params: any): Promise<any> => {
			console.log('SEMANTIC SEARCH TOOL CALLED WITH:', params);
			return {
				status: 'success',
				results: [`Found search results for: ${params.query}`]
			};
		},
		'product_lookup': async (params: any): Promise<any> => {
			console.log('PRODUCT LOOKUP TOOL CALLED WITH:', params);
			console.log('PRODUCT LOOKUP TOOL - orgId:', params.orgId);

			try {
				const query = params.query || '';
				const orgId = params.orgId || 0;
				console.log('PRODUCT LOOKUP TOOL - using orgId:', orgId);

				// Check if Shopify is connected for this organization
				const isShopifyConnected = await this.isShopifyConnected(orgId);

				if (!isShopifyConnected) {
					return {
						status: 'success',
						products: [],
						message: 'No products found - Shopify not connected'
					};
				}

				// Fetch products from Shopify via our API
				const products = await this.fetchProductsFromShopify(orgId);

				if (!products || products.length === 0) {
					return {
						status: 'success',
						products: [],
						message: 'No products found in store'
					};
				}

				// Search and rank products based on query
				const matchedProducts = this.searchAndRankProducts(products, query);

				// Log search results for debugging
				console.log(`Product search for "${query}" found ${matchedProducts.length} matches`);
				if (matchedProducts.length > 0) {
					console.log(`Top match: "${matchedProducts[0].title}" with score ${matchedProducts[0].score}`);
					console.log(`Match details: ${JSON.stringify(matchedProducts[0]._matchDetails)}`);
				}

				// Return top 5 matches (or fewer if there aren't 5 matches)
				const topMatches = matchedProducts.slice(0, 5);

				return {
					status: 'success',
					products: topMatches,
					query: query
				};
			} catch (error) {
				console.error('Error in product_lookup tool:', error);
				return {
					status: 'error',
					message: 'Failed to look up products',
					error: error.message
				};
			}
		},
		'metric_lookup': async (params: any): Promise<any> => {
				console.log('METRIC LOOKUP TOOL CALLED WITH:', params);
				console.log('METRIC LOOKUP TOOL - orgId:', params.orgId);

			try {
				const query = params.query || '';
				const orgId = params.orgId || 0;
				console.log('METRIC LOOKUP TOOL - using orgId:', orgId);

				// Create a cache key specific to this organization and query
				const cacheKey = `metrics:${orgId}:${query}`;

				// Try to get cached metrics first
				const cachedMetrics = this.metricCache.get<any[]>(cacheKey);
				if (cachedMetrics) {
					console.log(`Using cached metrics for org ${orgId} and query ${query}`);
					return {
						status: 'success',
						metrics: cachedMetrics,
						query: query
					};
				}

				// If not cached, fetch from ecommerce metric controller
				console.log(`Fetching fresh metrics for org ${orgId}`);
				const metrics = await this.ecommerceMetricController.fetchMetric(
					query,
					'none',
					'',
					'',
					'latest',
					orgId
				);

				if (!metrics) {
					return {
						status: 'success',
						metrics: [],
						message: 'No metrics found in store'
					};
				}

				// Cache the metrics
				this.metricCache.set(cacheKey, metrics);
				console.log(`Cached metrics for org ${orgId} and query ${query}`);

				return {
					status: 'success',
					metrics: metrics,
					query: query
				};
			} catch (error) {
				console.error('Error in metric_lookup tool:', error);
								return {
										status: 'error',
										message: 'Failed to look up metrics',
										error: error.message
								};
			}
		},
		'data_lookup': async (params: any): Promise<any> => {
			console.log('DATA LOOKUP TOOL CALLED WITH:', params);
			const orgId = params.orgId || 0;
			const template = await this.promptTemplateRepository.findOne({where: {name: 'DataTool'}});
			if (!template || !template.content) {
				throw new Error('DataTool prompt template not found');
			}
			const systemprompt = `The organizationId is ${orgId}. This cannot be overridden. ${template.content}`;
			const messages: CompletionMessage[] = [
				{role: 'system', content: systemprompt},
				{role: 'user', content: params.query}
			];

			const routerParams: RouterParams = {
					models: ['anthropic/claude-3.7-sonnet'],
					maxTokens: 1000
			};

			const llmResult = await this.llmRouter.executeCompletion(messages, routerParams);
			let llmQuery = llmResult.content.replace(/```/g, '').trim();
			if (!llmQuery.toLowerCase().includes(`organization = '${orgId}'`)) {
				const additionalPrompt = `Please ensure the query includes the organizationId ${orgId} in the WHERE clause. ${systemprompt}`;
				const additionalMessages: CompletionMessage[] = [
						{role: 'system', content: additionalPrompt},
						{role: 'user', content: params.query},
				];
				const additionalLLMResult = await this.llmRouter.executeCompletion(additionalMessages, routerParams);
				llmQuery = additionalLLMResult.content.replace(/```/g, '').trim();
			}
			if (!llmQuery.toLowerCase().includes(`organization = '${orgId}'`)) {
				throw new Error('Failed to add organizationId to query');
			}
			const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
			const path = `/${environment}/data`;
			const signedRequest = getURL(path, 'POST', {query: llmQuery}, DATA_API);
			console.log('DATA LOOKUP TOOL - signedRequest:');
			const response = await fetch(`https://${DATA_API}${path}`, signedRequest);
			const data = await response.json();

			return {status: 'success', data, query: llmQuery};
        },
		'best_sellers': async (params: any): Promise<any> => {
			console.log('BEST SELLERS TOOL CALLED WITH:', params);
			const orgId = params.orgId || 0;
			const timeframe = params.timeframe || '30d';
			const limit = params.limit || 10;

			try {
				// Build the timeframe condition
				let timeCondition = '';
				switch (timeframe) {
					case '7d':
						timeCondition = "AND created_at >= date_add('day', -7, current_date)";
						break;
					case '30d':
						timeCondition = "AND created_at >= date_add('day', -30, current_date)";
						break;
					case '90d':
						timeCondition = "AND created_at >= date_add('day', -90, current_date)";
						break;
					case '1y':
						timeCondition = "AND created_at >= date_add('year', -1, current_date)";
						break;
					case 'all':
					default:
						timeCondition = '';
						break;
				}

				// Build the SQL query for best sellers with sales volume and revenue
				const query = `
					WITH product_sales AS (
						SELECT
							item_name,
							item_id,
							SUM(item_quantity) as total_quantity_sold,
							SUM(item_quantity * item_price) as total_revenue,
							COUNT(DISTINCT id) as total_orders
						FROM "{database}".filtered_orders
						WHERE organization = '${orgId}'
							AND item_price > 0
							AND item_name IS NOT NULL
							AND item_name != ''
							${timeCondition}
						GROUP BY item_name, item_id
					)
					SELECT
						item_name,
						item_id,
						total_quantity_sold,
						ROUND(total_revenue, 2) as total_revenue,
						total_orders,
						ROUND(total_revenue / total_quantity_sold, 2) as avg_price_per_unit
					FROM product_sales
					ORDER BY total_quantity_sold DESC
					LIMIT ${limit}
				`;

				// Execute the query using the data lookup infrastructure
				const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
				const path = `/${environment}/data`;
				const signedRequest = getURL(path, 'POST', {query}, DATA_API);
				const response = await fetch(`https://${DATA_API}${path}`, signedRequest);
				const data = await response.json();

				if (!data || !data.Items) {
					return {
						status: 'success',
						products: [],
						message: 'No sales data found for the specified timeframe',
						timeframe,
						query
					};
				}

				// Format the results for better readability
				const formattedProducts = data.Items.map((item: any, index: number) => ({
					rank: index + 1,
					name: item.item_name,
					id: item.item_id,
					quantitySold: item.total_quantity_sold,
					totalRevenue: item.total_revenue,
					totalOrders: item.total_orders,
					avgPricePerUnit: item.avg_price_per_unit,
					displayText: `${item.item_name}: ${item.total_quantity_sold} sold, $${item.total_revenue}`
				}));

				return {
					status: 'success',
					products: formattedProducts,
					timeframe,
					totalProducts: formattedProducts.length,
					query
				};

			} catch (error) {
				console.error('Error in best_sellers tool:', error);
				return {
					status: 'error',
					message: 'Failed to fetch best sellers data',
					error: error.message,
					timeframe
				};
			}
		},
		'image_lookup': async (params: any): Promise<any> => {
			console.log('IMAGE LOOKUP TOOL CALLED WITH:', params);
			console.log('IMAGE LOOKUP TOOL - orgId:', params.orgId);

			try {
				const tag = params.tag?.trim();
				const description = params.description?.trim();
				const limit = params.limit || 10;
				const orgId = params.orgId || 0;
				console.log('IMAGE LOOKUP TOOL - using orgId:', orgId);

				// Fetch brand images with non-null and non-empty imageType
				const brandImages = await this.imageRepository.find({
					where: {
						orgId: orgId,
						imageType: {
							neq: '',
						}
					}
				});

				// Filter images that have AI tags
				let filteredImages = brandImages.filter(image =>
					image.imageType !== null &&
					image.imageType !== undefined &&
					image.imageType.trim() !== ''
				);

				if (filteredImages.length === 0) {
					return {
						status: 'success',
						images: [],
						message: 'No AI-tagged images found. Please add AI Tags to your images in the Assets section.',
						searchParams: { tag, description, limit }
					};
				}

				// First, attempt to filter by tag (partial match, case-insensitive) if provided
				let tagMatched = filteredImages;
				if (tag) {
					tagMatched = filteredImages.filter(image =>
						image.imageType?.trim().toLowerCase().includes(tag.toLowerCase())
					);
				}

				// Now, apply description filter if provided
				let results = tagMatched;
				let searchTerms: any[] = [];
				if (description) {
					searchTerms = description.toLowerCase().split(' ').filter((term: string) => term.length > 2);

					if (searchTerms.length > 0) {
						results = results.filter(image => {
							const imageDescription = (image.description || '').toLowerCase();
							const imageFriendlyname = (image.friendlyname || '').toLowerCase();
							// Check if any search terms match the image description or friendlyname
							return searchTerms.some((term: string) =>
								imageDescription.includes(term) || imageFriendlyname.includes(term)
							);
						});
					}
				}

				// Fallback: If no results with tag but description is provided, search by description on all filtered images
				if (results.length === 0 && tag && description && searchTerms.length > 0) {
					console.log(`No matches found with tag "${tag}". Falling back to description search only.`);
					results = filteredImages.filter(image => {
						const imageDescription = (image.description || '').toLowerCase();
						const imageFriendlyname = (image.friendlyname || '').toLowerCase();
						return searchTerms.some((term: string) =>
							imageDescription.includes(term) || imageFriendlyname.includes(term)
						);
					});
				}

				// Sort by relevance and limit results
				const limitedImages = results.slice(0, limit);

				// Format the results
				const formattedImages = limitedImages.map((image, index) => ({
					rank: index + 1,
					url: image.url,
					imageType: image.imageType,
					description: image.description || 'No description available',
					width: image.width || 0,
					height: image.height || 0,
					aspectRatio: calculateAspectRatio(image.width || 0, image.height || 0),
					displayText: `${image.imageType}: ${image.description || 'No description'}`
				}));

				console.log(`Image search found ${formattedImages.length} matches for tag: "${tag}", description: "${description}"`);

				return {
					status: 'success',
					images: formattedImages,
					totalImages: formattedImages.length,
					searchParams: { tag, description, limit }
				};

			} catch (error) {
				console.error('Error in image_lookup tool:', error);
				return {
					status: 'error',
					message: 'Failed to search images',
					error: error.message
				};
			}
		},
		'image_edit': async (params: any): Promise<any> => {
			console.log('IMAGE EDIT TOOL CALLED WITH:', params);
			console.log('IMAGE EDIT TOOL - orgId:', params.orgId);

			try {
				const imageUrl = params.image_url;
				const newText = params.new_text;
				const textToReplace = params.text_to_replace;
				const modificationDescription = params.modification_description;
				const model = params.model || 'flux1';
				const orgId = params.orgId || 0;

				if (!imageUrl) {
					return {
						status: 'error',
						message: 'Missing required parameter: image_url is required'
					};
				}

				// Determine edit type based on available parameters
				const isTextEdit = !!newText;
				const isModificationEdit = !!modificationDescription;
				const isRemoveText = !!textToReplace;

				// Validate that we have either text or modification parameters
				if (!isTextEdit && !isModificationEdit && !isRemoveText) {
					return {
						status: 'error',
						message: 'Either new_text or modification_description is required'
					};
				}

				// Prepare the prompt for image editing based on edit type
				let prompt = '';

				if (isTextEdit || isRemoveText) {
					// Parse styling from the new text for OpenAI model
					if (model === 'openai') {
						const { actualText, styling } = this.parseTextStyling(newText || '');
						console.log('Parsed text:', actualText);
						console.log('Parsed styling:', styling);

						if (textToReplace) {
							if (styling.length > 0) {
								prompt = `Change the text "${textToReplace}" to "${actualText}" on this image. Apply the following styling: ${styling.join(', ')}. Maintain the placement of the original text.`;
							} else {
								prompt = `Change the text "${textToReplace}" to "${actualText}" on this image. Maintain the style and placement of the original text.`;
							}
						} else {
							if (styling.length > 0) {
								prompt = `Add the text "${actualText}" to this image with the following styling: ${styling.join(', ')}. Place it in a visually appealing way that matches the image purpose.`;
							} else {
								prompt = `Add the text "${actualText}" to this image in a visually appealing way that matches the image style and purpose.`;
							}
						}
					} else {
						// For Flux1, use simpler prompt structure
						if (isTextEdit && newText.trim() && newText.trim().length > 0) {
							prompt = `Replace "${textToReplace}" with "${newText}" on this image`;
						} else if (isRemoveText) {
							prompt = `Remove the text "${textToReplace}" from this image`;
						} else {
							prompt = `Add the text "${newText}" to this image`;
						}
					}
				} else {
					// Handle non-text modifications
					if (model === 'openai') {
						prompt = `Make the following modification to this image: ${modificationDescription}. Ensure the changes look natural and maintain the overall quality and coherence of the image.`;
					} else {
						prompt = modificationDescription;
					}
				}

				// Route to appropriate implementation based on model
				if (model === 'flux1') {
					// Detect aspect ratio from the image
					let aspectRatio: string | undefined;
					try {
						const response = await fetch(imageUrl);
						const buffer = await response.arrayBuffer();
						const sharp = require('sharp');
						const metadata = await sharp(Buffer.from(buffer)).metadata();
						const width = metadata.width || 1024;
						const height = metadata.height || 1024;
						aspectRatio = calculateAspectRatio(width, height);
					} catch (error) {
						console.warn('Could not detect image dimensions, using 1:1 ratio:', error);
						aspectRatio = '1:1';
					}

					// Use Flux1 Kontext API with aspect ratio
					const result = await this.chatService.editImageFlux1(prompt, imageUrl, aspectRatio);
					await result.completionPromise;

					const message = await this.messageRepository.findById(result.messageId);
					const { editedImages = [] } = this.parseLLMMetadata(message);

					if (editedImages && editedImages.length > 0) {
						// Upload to S3 if needed (Flux1 returns URLs directly)
						const s3Urls = await Promise.all(
							editedImages.map(async (img: any) => {
								if (img.url.startsWith('http')) {
									return img.url; // Already a URL
								}
								return await this.uploadBase64ToS3(img.url);
							})
						);

						console.log(`Successfully processed ${s3Urls.length} Flux1 edited image variations`);

						return {
							status: 'success',
							prompt: prompt,
							edited_image_urls: s3Urls,
							original_url: imageUrl,
							edit_type: isTextEdit ? 'text' : 'modification',
							applied_text: isTextEdit ? newText : undefined,
							replaced_text: isTextEdit ? textToReplace : undefined,
							modification_description: isModificationEdit ? modificationDescription : undefined,
							model: 'flux1',
							variations_count: s3Urls.length
						};
					} else {
						return {
							status: 'error',
							message: 'Failed to edit image with Flux1',
							original_url: imageUrl
						};
					}
				} else {
					// Use OpenAI GPT-4 Vision (existing implementation)
					const detectedSize = await this.detectImageSize(imageUrl);
					console.log('Detected image size:', detectedSize);

					const result = await this.editImageStandalone({
						images: [imageUrl],
						prompt: prompt,
						quality: 'standard',
						size: detectedSize,
						n: 3  // Generate 3 variations
					});

					if (result.editedImages && result.editedImages.length > 0) {
						// Upload all base64 images to S3 and get public URLs
						const uploadPromises = result.editedImages.map(img => this.uploadBase64ToS3(img.url));
						const s3Urls = await Promise.all(uploadPromises);

						console.log(`Successfully processed ${s3Urls.length} OpenAI edited image variations`);

						return {
							status: 'success',
							edited_image_urls: s3Urls,
							original_url: imageUrl,
							edit_type: isTextEdit ? 'text' : 'modification',
							applied_text: isTextEdit ? newText : undefined,
							replaced_text: isTextEdit ? textToReplace : undefined,
							modification_description: isModificationEdit ? modificationDescription : undefined,
							detected_size: detectedSize,
							model: 'openai',
							variations_count: s3Urls.length
						};
					} else {
						return {
							status: 'error',
							message: 'Failed to edit image with OpenAI',
							original_url: imageUrl
						};
					}
				}
			} catch (error) {
				console.error('Error in image_edit tool:', error);
				return {
					status: 'error',
					message: 'Failed to edit image',
					error: error.message,
					model: params.model || 'flux1'
				};
			}
		},
		'image_info': async (params: any): Promise<any> => {
			console.log('IMAGE INFO TOOL CALLED WITH:', params);
			console.log('IMAGE INFO TOOL - orgId:', params.orgId);

			try {
				const imageUrl = params.url;
				const orgId = params.orgId || 0;

				if (!imageUrl) {
					return {
						status: 'error',
						message: 'Missing required parameter: url is required'
					};
				}

				// Import OpenAI service
				const openAiService = new (require('../services/open-ai.service').OpenAiService)();

				// Get image info using the OpenAI service
				const result = await openAiService.getImageInfo(imageUrl);

				// Parse the result
				let parsedResult;
				try {
					parsedResult = JSON.parse(result);
				} catch (parseError) {
					console.error('Error parsing OpenAI response:', parseError);
					return {
						status: 'error',
						message: 'Failed to parse image analysis result',
						error: parseError.message
					};
				}

				return {
					status: 'success',
					imageUrl: imageUrl,
					textContent: parsedResult,
					summary: parsedResult.summary || 'Image analyzed successfully'
				};

			} catch (error) {
				console.error('Error in image_info tool:', error);
				return {
					status: 'error',
					message: 'Failed to analyze image',
					error: error.message
				};
			}
		}
	};


	private readonly defaultRouterParams: RouterParams = {
		systems: ['OpenRouter', 'Anthropic', 'OpenAI', '*'],
		models: ['anthropic/claude-3.7-sonnet', 'anthropic/claude-3.5-sonnet'],
	};

        constructor(
                @repository(ConversationRepository)
                public conversationRepository: ConversationRepository,
                @repository(MessageRepository)
                private messageRepository: MessageRepository,
                @repository(PlannerCampaignImageRepository)
                private plannerCampaignImageRepository: PlannerCampaignImageRepository,
                @repository(PromptTemplateRepository)
                private promptTemplateRepository: PromptTemplateRepository,
                @repository(ImageRepository)
                private imageRepository: ImageRepository,
                @repository(OrganizationRepository)
                private organizationRepository: OrganizationRepository,
                @repository(TaskRepository)
                private taskRepository: TaskRepository,
                @service(PromptContextService) private promptContextService: PromptContextService,
                @service(LLMRouterService) private llmRouter: LLMRouterService,
                @service(ShopifyApiInvoker) private shopifyApiInvoker: ShopifyApiInvoker,
                @inject('services.ChatService')
                protected chatService: ChatService,
                @inject(RestBindings.Http.RESPONSE)
                private response: Response,
                @service(AdminUiController)
                private adminUiController: AdminUiController,
                @service(MemcachedLockService)
                private memcachedService: MemcachedLockService,
                @inject('controllers.EcommerceMetricController')
                private ecommerceMetricController: EcommerceMetricController,
                @service(MessageQuotaService) private quotaService: MessageQuotaService,
        ) {
		// Set the controller reference in the chat service
		this.chatService.setController(this);
	}

	@post('/chat/conversations/start')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async startConversation(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['message'],
						properties: {
							message: {
								oneOf: [
									{type: 'string'},
									{
										type: 'array',
										items: {
											type: 'object',
											required: ['type'],
											properties: {
												type: {
													type: 'string',
													enum: ['text', 'image_url']
												},
												text: {type: 'string'},
												image_url: {
													type: 'object',
													properties: {
														url: {type: 'string'},
														detail: {
															type: 'string',
															enum: ['low', 'high', 'auto']
														}
													},
													required: ['url']
												}
											}
										}
									}
								]
							},
							promptTemplateId: {type: 'number'},
							stream: {type: 'boolean', default: false},
							campaignId: {type: 'number'},
							taskId: {type: 'number'},
							tools: {
								type: 'array',
								items: {
									type: 'string'
								},
								description: 'List of tool names to enable for this conversation'
							},
						},
					},
				},
			},
		})
		body: StartConversationRequest,
		@injectUserOrgId()
		organizationId: number,
		@injectUserId()
		userId: number,
	) {
		console.log('startConversation - organizationId:', organizationId);
		const {message, promptTemplateId, campaignId, taskId, stream = false, tools = []} = body;

		// Get selected tools and their executors based on the request
		const selectedTools = this.getSelectedTools(tools);

		// Create tool executors with access to the organization ID
                const selectedToolExecutors = this.getSelectedToolExecutors(tools, organizationId);
                console.log('startConversation - selectedToolExecutors created with orgId:', organizationId);

                await this.quotaService.consume(organizationId, 1);

		const {conversationId, messageId, completionPromise, streamingUpdates} =
			await this.chatService.startConversation(message, {
				organizationId,
				createdByUserId: userId,
				promptTemplateId,
				campaignId,
				taskId,
				routerParams: {
					...this.defaultRouterParams,
					stream: true,
					tools: selectedTools.length > 0 ? selectedTools : undefined,
					toolExecutors: selectedTools.length > 0 ? selectedToolExecutors : undefined
				}
			});

		if (stream) {
			// Set up SSE headers
			this.response.setHeader('Content-Type', 'text/event-stream');
			this.response.setHeader('Cache-Control', 'no-cache');
			this.response.setHeader('Connection', 'keep-alive');

			// Start streaming updates
			try {
				// Send initial message with conversationId and messageId
				this.response.write(`data: ${JSON.stringify({
					conversationId,
					messageId,
					content: '',
					timestamp: new Date().toISOString(),
					status: 'started',
					toolCalls: undefined,
					toolResults: undefined
				})}\n\n`);

				const gen = streamingUpdates[Symbol.asyncIterator]();

				// prime the very first read
				let nextPull = gen.next();

				// we’ll keep the most recent toolCalls / toolResults here:
				let lastToolCalls: any;
				let lastToolResults: any;

				while (true) {
					// ① wait for whatever we primed last iteration
					const { value: chunk, done } = await nextPull;
					if (done) break;

					// ② immediately prime the *next* read
					nextPull = gen.next();              // installs resolveNext right now

					// Debug logging for streaming chunks
					if (process.env.DEBUG_STREAMING === 'true') {
						console.log(`[ChatController] Received chunk from generator: "${chunk}"`);
					}

					// Periodically check for tool calls and results
					try {
						const msg = await this.messageRepository.findById(messageId);
						lastToolCalls = msg.toolCalls ? JSON.parse(msg.toolCalls) : undefined;
						lastToolResults = msg.toolResults ? JSON.parse(msg.toolResults) : undefined;
					} catch (e) {
						console.error('messageRepository lookup failed:', e);
					}

					// ③ push the chunk to the client straight away
					this.response.write(`data: ${JSON.stringify({
						conversationId,
						messageId,
						content: chunk,
						timestamp: new Date().toISOString(),
						status: 'streaming',
						toolCalls: lastToolCalls,
						toolResults: lastToolResults,
					})}\n\n`);

					/* -----------------------------------------------------------
						④ fire-and-forget the slow work so it doesn’t block streaming
						----------------------------------------------------------- */
					// (async () => {
					// 	try {
					// 	const msg = await this.messageRepository.findById(messageId);
					// 		lastToolCalls   = msg.toolCalls   ? JSON.parse(msg.toolCalls)   : undefined;
					// 		lastToolResults = msg.toolResults ? JSON.parse(msg.toolResults) : undefined;
					// 	} catch (e) {
					// 	console.error('messageRepository lookup failed:', e);
					// 	}
					// })();
				}

				// Send final message to indicate completion
				const finalMessage = await this.messageRepository.findById(messageId);
				this.response.write(`data: ${JSON.stringify({
					conversationId,
					messageId,
					content: '',
					timestamp: new Date().toISOString(),
					status: 'completed',
					toolCalls: finalMessage.toolCalls ? JSON.parse(finalMessage.toolCalls) : undefined,
					toolResults: finalMessage.toolResults ? JSON.parse(finalMessage.toolResults) : undefined
				})}\n\n`);
			} catch (error) {
				console.error('Streaming error:', error);
				this.response.write(`data: ${JSON.stringify({
					conversationId,
					messageId,
					error: 'Streaming error occurred',
					timestamp: new Date().toISOString(),
					status: 'error',
					toolCalls: undefined,
					toolResults: undefined
				})}\n\n`);
			}

			// End the response
			this.response.end();
		} else {
			// Wait for completion and return final result
			await completionPromise;
			const conversation = await this.conversationRepository.findById(conversationId, {
				include: [{
					relation: 'messages',
					scope: {
						where: {id: messageId}
					}
				}]
			});

			const message = conversation.messages[0];
			return {
				conversationId,
				messageId,
				content: message?.content || '',
				toolCalls: message?.toolCalls ? JSON.parse(message.toolCalls) : undefined,
				toolResults: message?.toolResults ? JSON.parse(message.toolResults) : undefined,
			};
		}
	}

	@post('/chat/conversations/{id}/message')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async continueConversation(
		@modelIdForGuard(Conversation)
		@param.path.number('id') conversationId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['message'],
						properties: {
							message: {
								oneOf: [
									{type: 'string'},
									{
										type: 'array',
										items: {
											type: 'object',
											required: ['type'],
											properties: {
												type: {
													type: 'string',
													enum: ['text', 'image_url']
												},
												text: {type: 'string'},
												image_url: {
													type: 'object',
													properties: {
														url: {type: 'string'},
														detail: {
															type: 'string',
															enum: ['low', 'high', 'auto']
														}
													},
													required: ['url']
												}
											}
										}
									}
								]
							},
							stream: {type: 'boolean', default: false},
							updateSystemPrompt: {type: 'boolean', default: false},
							tools: {
								type: 'array',
								items: {
									type: 'string'
								},
								description: 'List of tool names to enable for this conversation'
							},
						},
					},
				},
			},
		})
		body: ContinueConversationRequest,
		@injectUserOrgId() organizationId: number,
		@injectUserId() userId: number,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	) {
		const {message, stream = false, updateSystemPrompt = false, tools = []} = body;

		// Get selected tools and their executors based on the request
		const selectedTools = this.getSelectedTools(tools);
		console.log('Selected tools:', tools);

		// Create tool executors with access to the organization ID
                const selectedToolExecutors = this.getSelectedToolExecutors(tools, organizationId);
                console.log('Tool executors created:', Object.keys(selectedToolExecutors));

                await this.quotaService.consume(organizationId, 1);

		// Make sure these are passed to the chat service
		const routerParams: RouterParams = {
			...this.defaultRouterParams,
			stream: true, // Always set to true for the router
			tools: selectedTools,
			toolExecutors: selectedToolExecutors
		};

		const result = await this.chatService.continueConversation(conversationId, message, routerParams, updateSystemPrompt, userId);

		// Extract messageId for use throughout the function
		const messageId = result.messageId;

		// For streaming response
		if (stream) {
			// Set up SSE headers
			response.setHeader('Content-Type', 'text/event-stream');
			response.setHeader('Cache-Control', 'no-cache');
			response.setHeader('Connection', 'keep-alive');

			// Start streaming updates
			try {
				// Send initial message with conversationId and messageId
				response.write(`data: ${JSON.stringify({
					conversationId,
					messageId,
					content: '',
					timestamp: new Date().toISOString(),
					status: 'started',
					toolCalls: undefined,
					toolResults: undefined
				})}\n\n`);

				const gen = result.streamingUpdates[Symbol.asyncIterator]();

				// prime the very first read
				let nextPull = gen.next();

				// we'll keep the most recent toolCalls / toolResults here:
				let lastToolCalls: any;
				let lastToolResults: any;

				while (true) {
					// ① wait for whatever we primed last iteration
					const { value: chunk, done } = await nextPull;
					if (done) break;

					// ② immediately prime the *next* read
					nextPull = gen.next();              // installs resolveNext right now

					// ③ push the chunk to the client straight away
					response.write(`data: ${JSON.stringify({
						conversationId,
						messageId,
						content: chunk,
						timestamp: new Date().toISOString(),
						status: 'streaming',
						toolCalls: lastToolCalls,
						toolResults: lastToolResults,
					})}\n\n`);
				}

				// Send final message to indicate completion
				const finalMessage = await this.messageRepository.findById(messageId);
				response.write(`data: ${JSON.stringify({
					conversationId,
					messageId,
					content: '',
					timestamp: new Date().toISOString(),
					status: 'completed',
					toolCalls: finalMessage.toolCalls ? JSON.parse(finalMessage.toolCalls) : undefined,
					toolResults: finalMessage.toolResults ? JSON.parse(finalMessage.toolResults) : undefined
				})}\n\n`);
			} catch (error) {
				console.error('Streaming error:', error);
				response.write(`data: ${JSON.stringify({
					conversationId,
					messageId,
					error: 'Streaming error occurred',
					timestamp: new Date().toISOString(),
					status: 'error',
					toolCalls: undefined,
					toolResults: undefined
				})}\n\n`);
			}

			// End the response
			response.end();
			return;
		} else {
			// Wait for completion and return final result
			await result.completionPromise;
			const conversation = await this.conversationRepository.findById(conversationId, {
				include: [{
					relation: 'messages',
					scope: {
						where: {id: messageId}
					}
				}]
			});

			const responseMessage = conversation.messages[0];
			return {
				conversationId,
				messageId,
				content: responseMessage?.content || '',
				toolCalls: responseMessage?.toolCalls ? JSON.parse(responseMessage.toolCalls) : undefined,
				toolResults: responseMessage?.toolResults ? JSON.parse(responseMessage.toolResults) : undefined,
			};
		}
	}

	@post('/chat/conversations/{id}/archive')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async archiveConversation(
		@modelIdForGuard(Conversation)
		@param.path.number('id') conversationId: number,
	): Promise<void> {
		await this.chatService.archiveConversation(conversationId);
	}

	@get('/chat/conversations/{id}')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@restrictReadsWithGuard({plural: false})
	async getConversation(
		@modelIdForGuard(Conversation)
		@param.path.number('id') conversationId: number,
	) {
		const conversation = await this.conversationRepository.findById(conversationId, {
			include: [{
				relation: 'messages',
				scope: {
					order: ['createdAt ASC']
				}
			}]
		});

		// Return null if conversation is archived
		if (conversation.isArchived) {
			throw new HttpErrors.NotFound('Conversation not found or archived');
		}

		return conversation;
	}

        @get('/chat/conversations')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@restrictReadsWithGuard({plural: true})
	@skipGuardCheck()
        async listConversations(
                @param.query.number('limit') limit = 10,
                @param.query.number('offset') offset = 0,
                @param.query.boolean('includeArchived') includeArchived = false,
                @param.query.string('sortField') sortField = 'createdAt',
                @param.query.string('sortOrder') sortOrder = 'DESC',
                @injectUserOrgId() organizationId: number,
        ) {
		const filter = {
			where: {
				organizationId,
				...(includeArchived ? {} : { archived: false })
			},
			include: [{
				relation: 'messages',
				scope: {
					limit: 1,
					order: ['createdAt DESC']
				}
			}, {
				relation: 'createdByUser'
			}],
			limit,
			offset,
			order: [`${sortField} ${sortOrder}`]
		} as Filter<Conversation>;

		const [conversations, count] = await Promise.all([
			this.conversationRepository.find(filter),
			this.conversationRepository.count(filter.where)
		]);

		return {
			data: conversations,
			meta: {
				total: count.count,
				limit,
				offset
			}
                };
        }

        @get('/chat/conversations/organization/{orgId}')
        @authorize({
                allowedRoles: ['raleon-admin'],
                voters: [basicAuthorization],
        })
        @skipGuardCheck()
        async listConversationsByOrg(
                @param.path.number('orgId') orgId: number,
                @param.query.number('limit') limit = 10,
                @param.query.number('offset') offset = 0,
                @param.query.boolean('includeArchived') includeArchived = false,
                @param.query.string('sortField') sortField = 'createdAt',
                @param.query.string('sortOrder') sortOrder = 'DESC',
        ) {
                const filter = {
                        where: {
                                organizationId: orgId,
                                ...(includeArchived ? {} : { archived: false })
                        },
                        include: [{
                                relation: 'messages',
                                scope: {
                                        limit: 1,
                                        order: ['createdAt DESC']
                                }
                        }, {
                                relation: 'createdByUser'
                        }],
                        limit,
                        offset,
                        order: [`${sortField} ${sortOrder}`]
                } as Filter<Conversation>;

                const [conversations, count] = await Promise.all([
                        this.conversationRepository.find(filter),
                        this.conversationRepository.count(filter.where)
                ]);

                return {
                        data: conversations,
                        meta: {
                                total: count.count,
                                limit,
                                offset
                        }
                };
        }

        @get('/chat/conversations/{id}/messages')
        @authorize({
                allowedRoles: ['raleon-admin'],
                voters: [basicAuthorization],
        })
        @skipGuardCheck()
        async listMessages(
                @param.path.number('id') conversationId: number,
                @param.query.number('limit') limit = 100,
                @param.query.number('offset') offset = 0,
                @param.query.string('sortOrder') sortOrder = 'ASC',
        ) {
                const filter = {
                        where: {conversationId},
                        limit,
                        offset,
                        order: [`createdAt ${sortOrder}`]
                } as Filter<Message>;

                const [messages, count] = await Promise.all([
                        this.messageRepository.find(filter),
                        this.messageRepository.count(filter.where)
                ]);

                return {
                        data: messages,
                        meta: {
                                total: count.count,
                                limit,
                                offset
                        }
                };
        }

	@post('/chat/conversations/new')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async newConversationForCampaign(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['campaignId'],
						properties: {
							campaignId: {type: 'number'},
							oldConversationId: {anyOf: [{type: 'number'}, {type: 'string'}]},
							taskId: {type: 'number'},
							name: {type: 'string'},
							promptTemplateId: {type: 'number'},
							clearOnly: {type: 'boolean'},
						},
					},
				},
			},
		})
		body: {
			campaignId: number;
			oldConversationId?: number | string;
			taskId?: number;
			name?: string;
			promptTemplateId?: number;
			clearOnly?: boolean;
		},
		@injectUserOrgId() organizationId: number,
		@injectUserId() userId: number,
	): Promise<{conversationId?: number; conversationMetadata?: any}> {
		const {campaignId, oldConversationId, taskId, name, promptTemplateId, clearOnly} = body;

		if (!campaignId) {
			throw new HttpErrors.BadRequest('campaignId is required');
		}

		// Find the task associated with the campaign
		const task = await this.taskRepository.findOne({where: {plannerCampaignId: campaignId}});

		// Variables to store old conversation details
		let oldConversation = null;
		let conversationName = 'New Chat';
		let conversationTaskId = task?.id;
		let conversationPromptTemplateId = undefined;

		// If oldConversationId is provided, get the old conversation details
		if (oldConversationId) {
			try {
				oldConversation = await this.conversationRepository.findById(Number(oldConversationId));
				if (oldConversation && oldConversation.organizationId === organizationId) {
					// Carry forward the old conversation details
					conversationName = oldConversation.name || 'New Chat';
					conversationTaskId = oldConversation.taskId;
					conversationPromptTemplateId = oldConversation.promptTemplateId;

					// Archive the old conversation
					await this.conversationRepository.updateById(Number(oldConversationId), {
						isArchived: true,
						taskId: undefined, // Unlink from task
						updatedAt: new Date(),
					});

					// If task exists, unlink it from the old conversation
					if (task && task.id) {
						try {
							await this.taskRepository.updateById(task.id, {
								conversationId: undefined,
							});
						} catch (error) {
							console.warn(`Could not unlink task ${task.id} from old conversation`, error);
						}
					}
				}
			} catch (error) {
				console.warn(`Could not find or archive old conversation ${oldConversationId}. It might have been deleted.`, error);
			}
		}

		// Override with explicitly provided values
		if (name) conversationName = name;
		if (taskId) conversationTaskId = taskId;
		if (promptTemplateId) conversationPromptTemplateId = promptTemplateId;

		// If clearOnly is true, don't create the conversation yet, just return metadata
		if (clearOnly) {
			return {
				conversationId: undefined,
				conversationMetadata: {
					name: conversationName,
					taskId: conversationTaskId,
					promptTemplateId: conversationPromptTemplateId,
					campaignId: campaignId,
					organizationId: organizationId,
				}
			};
		}

		// Create the new conversation with carried-forward details
		const conversation = await this.conversationRepository.create({
			organizationId,
			createdByUserId: userId,
			name: conversationName,
			taskId: conversationTaskId,
			promptTemplateId: conversationPromptTemplateId,
			campaignId: campaignId,
			isArchived: false,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		if (!conversation.id) {
			throw new HttpErrors.InternalServerError('Failed to create new conversation');
		}

		// Update the task's conversationId and originalConversationId if a task exists
		if (task && task.id) {
			try {
				await this.taskRepository.updateById(task.id, {
					conversationId: conversation.id,
					originalConversationId: conversation.id, // Set the dedicated field for product URLs
				});
			} catch (error) {
				console.warn(`Could not update task ${task.id} with new conversationId ${conversation.id}`, error);
			}
		}

		return {conversationId: conversation.id};
	}

        @patch('/chat/conversations/{id}/name')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
        async updateConversationName(
                @modelIdForGuard(Conversation)
                @param.path.number('id') conversationId: number,
                @requestBody({
                        content: {
                                'application/json': {
					schema: {
						type: 'object',
						required: ['name'],
						properties: {
							name: {type: 'string'},
						},
					},
				},
			},
		})
		body: {
			name: string;
		},
	) {
                await this.conversationRepository.updateById(conversationId, {
                        name: body.name,
                        updatedAt: new Date(),
                });
        }

	@post('/chat/conversations/{id}/compress')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async compressConversation(
		@modelIdForGuard(Conversation)
		@param.path.number('id') conversationId: number,
	): Promise<{summary: string; newConversationId: number}> {
		const conversation = await this.conversationRepository.findById(conversationId, {
			include: [{
				relation: 'messages',
				scope: {
					order: ['createdAt ASC']
				}
			}]
		});

		if (!conversation.messages || conversation.messages.length === 0) {
			throw new HttpErrors.BadRequest('Conversation has no messages to summarize.');
		}

		const chatHistory = conversation.messages.map(m => `${m.role}: ${m.content}`).join('\n');
		const prompt = `Please summarize the following conversation:\n\n${chatHistory}`;

		const routerParams: RouterParams = {
			...this.defaultRouterParams,
			stream: false,
		};

		const result = await this.chatService.startConversation(prompt, {
			organizationId: conversation.organizationId,
			routerParams,
		});

		await result.completionPromise;

		const message = await this.messageRepository.findById(result.messageId);
		const summary = message.content || '';

		// Archive the old conversation
		await this.conversationRepository.updateById(conversationId, {
			isArchived: true,
			taskId: undefined, // Unlink from task
			updatedAt: new Date(),
		});

		// Create a new conversation with the summary as the first message
		const newConversation = await this.conversationRepository.create({
			organizationId: conversation.organizationId,
			createdByUserId: conversation.createdByUserId, // Preserve original creator
			name: conversation.name ? `${conversation.name} (Compressed)` : 'Compressed Chat',
			promptTemplateId: conversation.promptTemplateId,
			taskId: conversation.taskId,
			campaignId: conversation.campaignId,
			isArchived: false,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		if (!newConversation.id) {
			throw new HttpErrors.InternalServerError('Failed to create new conversation');
		}

		// Update the task with the new conversation ID if a task exists
		if (conversation.taskId) {
			try {
				await this.taskRepository.updateById(conversation.taskId, {
					conversationId: newConversation.id,
				});
			} catch (error) {
				console.warn(`Could not update task ${conversation.taskId} with new conversationId ${newConversation.id}`, error);
			}
		}

		// Add the summary as the first user message in the new conversation
		await this.messageRepository.create({
			conversationId: newConversation.id,
			role: MessageRole.USER,
			content: summary,
			status: MessageStatus.COMPLETED,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		return { summary, newConversationId: newConversation.id };
	}

        @del('/chat/conversations/{id}')
        @authorize({
                allowedRoles: ['admin', 'support', 'customer'],
                voters: [basicAuthorization],
        })
        async deleteConversation(
                @modelIdForGuard(Conversation)
                @param.path.number('id') conversationId: number,
        ) {
                const convo = await this.conversationRepository.findById(conversationId);
                if (convo.taskId) {
                        throw new HttpErrors.BadRequest('Cannot delete a conversation linked to a task');
                }

                await this.chatService.deleteConversation(conversationId);
        }

	@post('/imagegen/generate-image')
	@authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
	})
	@skipGuardCheck()
        async generateImageStandalone(
          @requestBody({
	    content: {
	      'application/json': {
	        schema: {
	          type: 'object',
	          required: ['prompt'],
	          properties: {
	            prompt: { type: 'string' },
	            quality: { type: 'string', enum: ['standard', 'high'] },
	            style: { type: 'string', enum: ['vivid', 'natural'] },
	            size: { type: 'string', enum: ['1024x1024', '1024x1536', '1536x1024'] },
	            background: { type: 'string', enum: ['auto', 'transparent', 'opaque'] },
	            n: { type: 'number', minimum: 1, maximum: 4 },
	            campaignId: { type: 'number' }
	          },
	        },
	      },
	    },
	  })
          body: {
            prompt: string;
            quality?: 'standard' | 'high';
            style?: 'vivid' | 'natural';
            size?: '1024x1024' | '1024x1536' | '1536x1024';
            background?: 'auto' | 'transparent' | 'opaque';
            n?: number;
            campaignId?: number;
          },
          @injectUserOrgId() orgId: number,
        ) {
          await this.quotaService.consume(orgId, 1);
          return this.generateImageImpl(body);
        }

	@get('/imagegen/status/{id}')
	@authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async checkImageGenerationStatus(
	  @param.path.number('id') placeholderId: number,
	) {
	  // Find the placeholder image record
	  const placeholder = await this.plannerCampaignImageRepository.findById(placeholderId);

	  if (!placeholder) {
	    return {
	      status: 'error',
	      message: 'Image generation job not found',
	    };
	  }

	  // Check if the image is still generating
	  if (placeholder.imageId === -1) {
	    return {
	      status: 'generating',
	      message: 'Image is still being generated',
	    };
	  }

	  // Check if the image generation failed
	  if (placeholder.imageId === -2) {
	    return {
	      status: 'failed',
	      message: 'Image generation failed',
	    };
	  }

	  // If we have a URL, the image is ready
	  if (placeholder.url) {
	    return {
	      status: 'completed',
	      url: placeholder.url,
	      isDraft: placeholder.isDraft,
	      imageId: placeholder.imageId,
	    };
	  }

	  // Default fallback
	  return {
	    status: 'unknown',
	    message: 'Unknown image generation status',
	  };
	}

	@post('/chat/conversations/{id}/generate-image')
	@authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
	})
	async generateImage(
	  @modelIdForGuard(Conversation)
	  @param.path.number('id') conversationId: number,
	  @requestBody({
	    content: {
	      'application/json': {
	        schema: {
	          type: 'object',
	          required: ['prompt'],
	          properties: {
	            prompt: { type: 'string' },
	            quality: { type: 'string', enum: ['standard', 'high'] },
	            style: { type: 'string', enum: ['vivid', 'natural'] },
	            size: { type: 'string', enum: ['1024x1024', '1024x1536', '1536x1024'] },
	            background: { type: 'string', enum: ['auto', 'transparent', 'opaque'] },
	            n: { type: 'number', minimum: 1, maximum: 4 }
	          },
	        },
	      },
	    },
	  })
	  body: {
	    prompt: string;
	    quality?: 'standard' | 'high';
	    style?: 'vivid' | 'natural';
	    size?: '1024x1024' | '1024x1536' | '1536x1024';
	    background?: 'auto' | 'transparent' | 'opaque';
	    n?: number;
	  },
        ) {
          const convo = await this.conversationRepository.findById(conversationId);
          await this.quotaService.consume(convo.organizationId, 1);
          const result = await this.generateImageImpl(body, conversationId);
          return result;
        }

	private async generateImageImpl(
	  params: {
	    prompt: string;
	    quality?: 'standard' | 'high';
	    style?: 'vivid' | 'natural';
	    size?: '1024x1024' | '1024x1536' | '1536x1024';
	    background?: 'auto' | 'transparent' | 'opaque';
	    n?: number;
	    campaignId?: number; // Add campaignId parameter
	  },
	  conversationId?: number
	) {
	  // Create a placeholder image record if campaignId is provided
	  let placeholderImageId: number | undefined;
	  if (params.campaignId) {
	    const placeholder = await this.plannerCampaignImageRepository.create({
	      plannerCampaignId: params.campaignId,
	      url: '', // Empty URL indicates it's still generating
	      isDraft: true,
	      // Store additional metadata as needed
	      imageId: -1, // Use -1 to indicate it's a placeholder for a generating image
	    });
	    placeholderImageId = placeholder.id;
	  }

	  const routerParams = {
	    systems: ['OpenAI'],
	    providers: ['OpenAI'],
	    models: ['openai/gpt-image-1'],
	    imageGeneration: {
	      prompt: params.prompt,
	      quality: params.quality || 'standard',
	      style: params.style || 'vivid',
	      size: params.size || '1024x1024',
	      background: params.background,
	      n: params.n || 1
	    }
	  };

	  let messageId: number;
	  let completionPromise: Promise<void>;

	  // Start the image generation process
	  if (conversationId) {
	    const result = await this.chatService.continueConversation(
	      conversationId,
	      params.prompt,
	      routerParams,
	      false, // updateSystemPrompt
	      undefined // createdByUserId - undefined for system-generated image requests
	    );
	    messageId = result.messageId;
	    completionPromise = result.completionPromise;
	  } else {
	    const result = await this.chatService.generateImage(
	      params.prompt,
	      routerParams
	    );
	    messageId = result.messageId;
	    completionPromise = result.completionPromise;
	  }

	  // Process the image generation asynchronously if we have a placeholder
	  if (placeholderImageId) {
	    // Don't await this promise - let it run in the background
	    this.processImageGenerationAsync(
	      completionPromise,
	      messageId,
	      placeholderImageId,
	      params.campaignId as number
	    ).catch(err => {
	      console.error('Error in async image generation process:', err);
	    });

	    // Return the placeholder ID immediately
	    return {
	      messageId,
	      placeholderImageId,
	      status: 'generating',
	      content: '',
	      generatedImages: [],
	    };
	  }

	  // If no placeholder (no campaignId), wait for completion as before
	  await completionPromise;

	  if (conversationId) {
	    const conversation = await this.conversationRepository.findById(conversationId, {
	      include: [{
	        relation: 'messages',
	        scope: {
	          where: { id: messageId }
	        }
	      }]
	    });
	    const message = conversation.messages[0];
	    const { generatedImages = [] } = this.parseLLMMetadata(message);
	    return {
	      messageId,
	      content: message?.content || '',
	      generatedImages,
	    };
	  } else {
	    const message = await this.messageRepository.findById(messageId);
	    const { generatedImages = [] } = this.parseLLMMetadata(message);
	    return {
	      messageId,
	      content: message?.content || '',
	      generatedImages,
	    };
	  }
	}

	// Helper method to process image generation asynchronously
	private async processImageGenerationAsync(
	  completionPromise: Promise<void>,
	  messageId: number,
	  placeholderImageId: number,
	  campaignId: number
	): Promise<void> {
	  try {
	    // Wait for the image generation to complete
	    await completionPromise;

	    // Get the generated image data
	    const message = await this.messageRepository.findById(messageId);
	    const { generatedImages = [] } = this.parseLLMMetadata(message);
		const { editedImages = [] } = this.parseLLMMetadata(message);
		//combine generated and edited images
		const allImages = [...generatedImages, ...editedImages];

	    if (allImages.length === 0) {
	      // No images were generated, update the placeholder to indicate failure
	      await this.plannerCampaignImageRepository.updateById(placeholderImageId, {
	        url: '',
	        isDraft: false,
	        imageId: -2, // Use -2 to indicate generation failed
	      });
	      return;
	    }

	    // Get the first generated image
	    const generatedImage = allImages[0];

	    // The URL is a base64 data URL, we need to upload it to S3
	    try {
	      // Convert base64 data URL to Buffer
	      const base64Data = generatedImage.url.split(',')[1];
	      if (!base64Data) {
	        throw new Error('Invalid base64 data URL');
	      }

	      const imageBuffer = Buffer.from(base64Data, 'base64');

	      // Generate a unique filename
	      const filename = `generated-${Date.now()}-${Math.random().toString(36).substring(2)}.png`;

	      // Use the AWS SDK directly to upload to S3
	      const AWS = require('aws-sdk');
	      const s3 = new AWS.S3({
	        accessKeyId: process.env.API_ACCESS_KEY,
	        secretAccessKey: process.env.API_SECRET_KEY,
	        region: 'us-east-1'
	      });

	      // Upload to S3
	      await s3.putObject({
	        Bucket: 'raleon-images-cdn',
	        Key: filename,
	        Body: imageBuffer,
	        ContentType: 'image/png',
	        ACL: 'public-read'
	      }).promise();

	      // Generate the S3 URL
	      const s3Domain = 'd3q4ufbgs1i4ak.cloudfront.net';
	      const s3Path = '/' + encodeURIComponent(filename);
	      const s3Url = `https://${s3Domain}${s3Path}`;

	      console.log('Uploaded image to S3:', s3Url);

	      // Update the placeholder with the S3 URL
	      await this.plannerCampaignImageRepository.updateById(placeholderImageId, {
	        url: s3Url,
	        isDraft: true, // Keep as draft until explicitly saved to assets
	        imageId: undefined, // Clear the -1 placeholder value
	      });

	      console.log(`Image generation complete. Updated placeholder ${placeholderImageId} with S3 URL: ${s3Url}`);
	    } catch (uploadError) {
	      console.error('Error uploading generated image to S3:', uploadError);

	      // If S3 upload fails, fall back to using the base64 URL
	      await this.plannerCampaignImageRepository.updateById(placeholderImageId, {
	        url: generatedImage.url,
	        isDraft: true,
	        imageId: undefined,
	      });

	      console.log(`Image generation complete but S3 upload failed. Using base64 URL for placeholder ${placeholderImageId}`);
	    }
	  } catch (error) {
	    console.error('Error processing image generation:', error);

	    // Update the placeholder to indicate failure
	    try {
	      await this.plannerCampaignImageRepository.updateById(placeholderImageId, {
	        url: '',
	        isDraft: false,
	        imageId: -2, // Use -2 to indicate generation failed
	      });
	    } catch (updateError) {
	      console.error('Error updating placeholder after generation failure:', updateError);
	    }
	  }
	}

  @post('/imagegen/edit-image-flux1')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async editImageFlux1Standalone(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['image_url', 'prompt'],
            properties: {
              image_url: { type: 'string', description: 'Direct image URL to edit' },
              prompt: { type: 'string', description: 'Description of the edit to apply' },
              aspect_ratio: {
                type: 'string',
                description: 'Desired aspect ratio (e.g., "16:9"). Supports ratios from 3:7 to 7:3',
                enum: ['3:7', '9:19', '1:2', '9:16', '2:3', '3:4', '4:5', '1:1', '5:4', '4:3', '3:2', '16:9', '2:1', '19:9', '7:3']
              },
              campaignId: { type: 'number' }
            },
          },
        },
      },
    })
    body: {
      image_url: string;
      prompt: string;
      aspect_ratio?: string;
      campaignId?: number;
    },
    @injectUserOrgId() orgId: number,
  ) {
    await this.quotaService.consume(orgId, 1);

    // Create a placeholder image record if campaignId is provided
    let placeholderImageId: number | undefined;
    if (body.campaignId) {
      const placeholder = await this.plannerCampaignImageRepository.create({
        plannerCampaignId: body.campaignId,
        url: '', // Empty URL indicates it's still generating
        isDraft: true,
        imageId: -1, // Use -1 to indicate it's a placeholder for a generating image
      });
      placeholderImageId = placeholder.id;
    }

    const { messageId, completionPromise } = await this.chatService.editImageFlux1(
      body.prompt,
      body.image_url,
      body.aspect_ratio
    );

    // Process the image editing asynchronously if we have a placeholder
    if (placeholderImageId) {
      // Don't await this promise - let it run in the background
      this.processImageGenerationAsync(
        completionPromise,
        messageId,
        placeholderImageId,
        body.campaignId as number
      ).catch(err => {
        console.error('Error in async Flux1 image editing process:', err);
      });

      // Return the placeholder ID immediately
      return {
        messageId,
        placeholderImageId,
        status: 'generating',
        content: '',
        editedImages: [],
      };
    }

    // If no placeholder (no campaignId), wait for completion
    await completionPromise;

    const message = await this.messageRepository.findById(messageId);
    const { editedImages = [] } = this.parseLLMMetadata(message);

    return {
      messageId,
      content: message?.content || '',
      editedImages,
    };
  }

  @post('/imagegen/edit-image')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async editImageStandalone(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['images', 'prompt'],
            properties: {
              images: {
                type: 'array',
                items: { type: 'string', description: 'Direct image URLs' },
                minItems: 1
              },
              mask: { type: 'string', description: 'Mask image URL' },
              prompt: { type: 'string' },
              quality: { type: 'string', enum: ['standard', 'high'] },
              size: { type: 'string', enum: ['1024x1024', '1536x1024', '1024x1536'] },
              n: { type: 'number', minimum: 1, maximum: 4 },
              campaignId: { type: 'number' }
            },
          },
        },
      },
    })
    body: {
      images: string[]; // Direct image URLs
      mask?: string; // Mask image URL
      prompt: string;
      quality?: 'standard' | 'high';
      size?: '1024x1024' | '1536x1024' | '1024x1536';
      n?: number;
      campaignId?: number;
    },
  ) {
    // Create a placeholder image record if campaignId is provided
    let placeholderImageId: number | undefined;
    if (body.campaignId) {
      const placeholder = await this.plannerCampaignImageRepository.create({
        plannerCampaignId: body.campaignId,
        url: '', // Empty URL indicates it's still generating
        isDraft: true,
        // Store additional metadata as needed
        imageId: -1, // Use -1 to indicate it's a placeholder for a generating image
      });
      placeholderImageId = placeholder.id;
    }

    // Helper function to convert URL to data URL
    const urlToDataUrl = async (url: string): Promise<string> => {
      const response = await fetch(url);
      const contentType = response.headers.get('content-type');
      if (!contentType) {
        throw new Error('No content-type in response');
      }
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      return `data:${contentType};base64,${buffer.toString('base64')}`;
    };

    // Convert image URLs to data URLs
    const imageDataUrls = await Promise.all(
      body.images.map(url => urlToDataUrl(url))
    );

    // Convert mask URL to data URL if present
    let maskDataUrl: string | undefined;
    if (body.mask) {
      maskDataUrl = await urlToDataUrl(body.mask);
    }

    // Create model string that includes quality info
    const modelWithQuality = `gpt-image-1${body.quality === 'high' ? ':high' : ''}`;

    const { messageId, completionPromise } = await this.chatService.editImage(
      body.prompt,
      imageDataUrls, // Only pass the converted data URLs
      maskDataUrl,
      {
        models: [modelWithQuality],
        imageEditing: {
          imageDataUrls,
          maskDataUrl,
          quality: body.quality,
          prompt: body.prompt,
          size: body.size || '1024x1024',
          n: body.n || 1
        }
      }
    );

    // Process the image editing asynchronously if we have a placeholder
    if (placeholderImageId) {
      // Don't await this promise - let it run in the background
      this.processImageGenerationAsync(
        completionPromise,
        messageId,
        placeholderImageId,
        body.campaignId as number
      ).catch(err => {
        console.error('Error in async image editing process:', err);
      });

      // Return the placeholder ID immediately
      return {
        messageId,
        placeholderImageId,
        status: 'generating',
        content: '',
        editedImages: [],
      };
    }

    // If no placeholder (no campaignId), wait for completion as before
    await completionPromise;

    const message = await this.messageRepository.findById(messageId);
    const { editedImages = [] } = this.parseLLMMetadata(message);

    return {
      messageId,
      content: message?.content || '',
      editedImages,
    };
  }

  @post('/chat/conversations/{id}/edit-image')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  async editImage(
    @modelIdForGuard(Conversation)
    @param.path.number('id') conversationId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['images', 'prompt'],
            properties: {
              images: {
                type: 'array',
                items: { type: 'string', description: 'Image references like #img0' },
                minItems: 1
              },
              mask: { type: 'string', description: 'Image reference like #img0' },
              prompt: { type: 'string' },
              quality: { type: 'string', enum: ['standard', 'high'] },
              size: { type: 'string', enum: ['1024x1024', '1024x1536', '1536x1024'] },
              n: { type: 'number', minimum: 1, maximum: 4 },
              campaignId: { type: 'number' }
            },
          },
        },
      },
    })
    body: {
      images: string[]; // Image references like #img0
      mask?: string; // Image reference like #img0
      prompt: string;
      quality?: 'standard' | 'high';
      size?: '1024x1024' | '1024x1536' | '1536x1024';
      n?: number;
      campaignId?: number;
    },
  ) {
    // Create a placeholder image record if campaignId is provided
    let placeholderImageId: number | undefined;
    if (body.campaignId) {
      const placeholder = await this.plannerCampaignImageRepository.create({
        plannerCampaignId: body.campaignId,
        url: '', // Empty URL indicates it's still generating
        isDraft: true,
        // Store additional metadata as needed
        imageId: -1, // Use -1 to indicate it's a placeholder for a generating image
      });
      placeholderImageId = placeholder.id;
    }

    // Get all messages from the conversation to look for image references
    const conversationWithMessages = await this.conversationRepository.findById(conversationId, {
      include: [{
        relation: 'messages',
        scope: {
          order: ['createdAt ASC']
        }
      }]
    });

    const messages = conversationWithMessages.messages || [];

    // Function to find image URL for a reference like #img0
    const findImageUrl = (imageRef: string): string | undefined => {
      // Extract the index from #imgN format
      const matches = imageRef.match(/^#img(\d+)$/);
      if (!matches) return undefined;

      const targetIndex = parseInt(matches[1], 10);
      let currentImageIndex = 0;

      for (const msg of messages) {
        try {
          // First check metadata for generated/edited images
          const metadata = msg.llmMetadata ?
            (typeof msg.llmMetadata === 'string' ? JSON.parse(msg.llmMetadata) : msg.llmMetadata)
            : undefined;

          // Look through generated and edited images
          const allImages = [
            ...(metadata?.generatedImages || []),
            ...(metadata?.editedImages || [])
          ];

          for (const img of allImages) {
            if (currentImageIndex === targetIndex) {
              return img.url;
            }
            currentImageIndex++;
          }

          // Then check message content for multipart content with image_url types
          if (msg.content) {
            try {
              const contentArray = JSON.parse(msg.content);
              if (Array.isArray(contentArray)) {
                const imageUrls = contentArray
                  .filter((content): content is ImageUrlContent =>
                    content.type === 'image_url' && !!content.image_url?.url);

                for (const imgContent of imageUrls) {
                  if (currentImageIndex === targetIndex) {
                    return imgContent.image_url.url;
                  }
                  currentImageIndex++;
                }
              }
            } catch (contentParseError) {
              // Content was not JSON or not an array, skip
            }
          }
        } catch (e) {
          console.error('Error parsing message metadata:', e);
        }
      }
      return undefined;
    };

    // Convert image references to Blobs
    const imageDataUrls = await Promise.all(body.images.map(async (imageRef) => {
      const imageUrl = findImageUrl(imageRef);
      if (!imageUrl) {
        throw new Error(`Image reference ${imageRef} not found in conversation history`);
      }

      return imageUrl;
    }));

    // Convert mask reference to Blob if present
    let maskDataUrl: string | undefined;
    if (body.mask) {
      maskDataUrl = findImageUrl(body.mask);
    }

    // Create model string that includes quality info
    const modelWithQuality = `openai/gpt-image-1${body.quality === 'high' ? ':high' : ''}`;
    const convo = await this.conversationRepository.findById(conversationId);
    await this.quotaService.consume(convo.organizationId, 1);
    const { messageId, completionPromise } = await this.chatService.continueConversation(
      conversationId,
      body.prompt,
      {
        systems: ['OpenAI'],
        providers: ['OpenAI'],
        models: [modelWithQuality],
        imageEditing: {
          imageDataUrls,
          maskDataUrl,
          prompt: body.prompt,
          size: body.size || '1024x1024',
          n: body.n || 1
        }
      },
      false, // updateSystemPrompt
      undefined // createdByUserId - undefined for system-generated image editing requests
    );

    // Process the image editing asynchronously if we have a placeholder
    if (placeholderImageId) {
      // Don't await this promise - let it run in the background
      this.processImageGenerationAsync(
        completionPromise,
        messageId,
        placeholderImageId,
        body.campaignId as number
      ).catch(err => {
        console.error('Error in async image editing process:', err);
      });

      // Return the placeholder ID immediately
      return {
        messageId,
        placeholderImageId,
        status: 'generating',
        content: '',
        editedImages: [],
      };
    }

    // If no placeholder (no campaignId), wait for completion as before
    await completionPromise;

    const conversation = await this.conversationRepository.findById(conversationId, {
      include: [{
        relation: 'messages',
        scope: {
          where: { id: messageId }
        }
      }]
    });

    const message = conversation.messages[0];
    const { editedImages = [] } = this.parseLLMMetadata(message);

    return {
      messageId,
      content: message?.content || '',
      editedImages,
    };
  }  private parseLLMMetadata(message: MessageWithMetadata): LLMMetadata {
    try {
      if (!message?.llmMetadata) return { generatedImages: [], editedImages: [] };

      const metadata = typeof message.llmMetadata === 'string' ?
        JSON.parse(message.llmMetadata) :
        message.llmMetadata as Record<string, unknown>;

      return {
        ...metadata as LLMMetadata,
        generatedImages: (metadata.generatedImages as Array<{url: string; revised_prompt?: string}>) || [],
        editedImages: (metadata.editedImages as Array<{url: string}>) || []
      };
    } catch (e) {
      console.error('Failed to parse LLM metadata:', e);
      return { generatedImages: [], editedImages: [] };
    }
  }

  /**
   * Helper method to get selected tools based on tool names
   */
  /**
   * Helper function to parse styling from text
   */
  private parseTextStyling(text: string): { actualText: string; styling: string[] } {
    const styleKeywords = [
      // Colors
      'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'black', 'white', 'gray', 'grey',
      'gold', 'silver', 'brown', 'cyan', 'magenta', 'teal', 'navy', 'maroon', 'lime',
      // Styles
      'bold', 'italic', 'underlined', 'uppercase', 'lowercase', 'capitalized',
      // Sizes
      'large', 'small', 'huge', 'tiny', 'big', 'medium',
      // Fonts
      'serif', 'sans-serif', 'cursive', 'monospace', 'handwritten',
      // Effects
      'glowing', 'shadowed', 'outlined', 'gradient', '3d', 'neon', 'metallic'
    ];

    const words = text.split(' ');
    const styling: string[] = [];
    const actualWords: string[] = [];

    // Check each word to see if it's a styling keyword
    let foundTextKeyword = false;
    for (let i = 0; i < words.length; i++) {
      const word = words[i].toLowerCase();

      // Check if this word is followed by "text" or "font"
      if ((word === 'text' || word === 'font') && i > 0) {
        foundTextKeyword = true;
        continue; // Skip the word "text" or "font"
      }

      // If we haven't found the actual text yet and this is a style keyword
      if (!foundTextKeyword && styleKeywords.includes(word)) {
        styling.push(words[i]); // Keep original case for the style
      } else {
        // This is part of the actual text
        actualWords.push(words[i]);
        foundTextKeyword = true; // Everything after this is actual text
      }
    }

    // If no text was found after parsing, treat the whole thing as text
    if (actualWords.length === 0) {
      return { actualText: text, styling: [] };
    }

    return {
      actualText: actualWords.join(' '),
      styling: styling
    };
  }

  /**
   * Helper function to detect image aspect ratio using Sharp
   */
  private async detectImageSize(imageUrl: string): Promise<'1024x1024' | '1536x1024' | '1024x1536'> {
    try {
      const sharp = require('sharp');

      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        console.warn('Could not fetch image for size detection, using default');
        return '1024x1024';
      }

      // Get the image buffer
      const buffer = await response.arrayBuffer();
      const imageBuffer = Buffer.from(buffer);

      // Get image metadata using Sharp
      const metadata = await sharp(imageBuffer).metadata();
      const width = metadata.width || 1024;
      const height = metadata.height || 1024;
      const aspectRatio = width / height;

      console.log(`Original image dimensions: ${width}x${height}, aspect ratio: ${aspectRatio}`);

      // Determine best fit size based on aspect ratio
      if (Math.abs(aspectRatio - 1) < 0.1) {
        // Square or close to square
        return '1024x1024';
      } else if (aspectRatio > 1.3) {
        // Landscape
        return '1536x1024';
      } else if (aspectRatio < 0.8) {
        // Portrait
        return '1024x1536';
      } else {
        // Default to square for other ratios
        return '1024x1024';
      }
    } catch (error) {
      console.warn('Error detecting image size:', error);
      return '1024x1024';
    }
  }

  /**
   * Helper function to upload base64 image to S3 and return public URL
   */
  private async uploadBase64ToS3(base64Url: string): Promise<string> {
    try {
      // Check if it's already a regular URL (not base64)
      if (!base64Url.startsWith('data:')) {
        return base64Url; // Return as-is if it's already a regular URL
      }

      const AWS = require('aws-sdk');
      const s3 = new AWS.S3({
        accessKeyId: process.env.API_ACCESS_KEY,
        secretAccessKey: process.env.API_SECRET_KEY,
        region: 'us-east-1'
      });

      // Extract base64 data from data URL
      const base64Data = base64Url.split(',')[1];
      if (!base64Data) {
        throw new Error('Invalid base64 data URL');
      }

      const imageBuffer = Buffer.from(base64Data, 'base64');
      const filename = `edited-${Date.now()}-${Math.random().toString(36).substring(2)}.png`;

      // Upload to S3
      await s3.putObject({
        Bucket: 'raleon-images-cdn',
        Key: filename,
        Body: imageBuffer,
        ContentType: 'image/png',
        ACL: 'public-read'
      }).promise();

      // Generate the S3 URL
      const s3Domain = 'd3q4ufbgs1i4ak.cloudfront.net';
      const s3Path = '/' + encodeURIComponent(filename);
      const s3Url = `https://${s3Domain}${s3Path}`;

      console.log('Uploaded edited image to S3:', s3Url);
      return s3Url;
    } catch (error) {
      console.error('Error uploading to S3:', error);
      // Return original URL as fallback
      return base64Url;
    }
  }

  private getSelectedTools(toolNames: string[]): ToolDefinition[] {
    return toolNames
      .filter(name => this.availableTools[name])
      .map(name => this.availableTools[name]);
  }

  /**
   * Helper method to get selected tool executors based on tool names
   * Updated to include organization ID
   */
  private getSelectedToolExecutors(toolNames: string[], orgId: number): {
    [functionName: string]: (args: any) => Promise<any>;
  } {
    console.log('getSelectedToolExecutors - orgId received:', orgId);
    const executors: {
      [functionName: string]: (args: any) => Promise<any>;
    } = {};

    toolNames.forEach(name => {
      if (this.toolExecutors[name]) {
        // Create a wrapper that injects the organization ID into the arguments
        executors[name] = async (args: any) => {
          console.log(`Tool executor for ${name} - original args:`, args);
          // Add the organization ID to the arguments
          const argsWithOrg = { ...args, orgId };
          console.log(`Tool executor for ${name} - args with orgId:`, argsWithOrg);
          // Call the original executor with the enhanced arguments
          return this.toolExecutors[name](argsWithOrg);
        };
      }
    });

    return executors;
  }

  // Helper methods for product_lookup tool
  private async isShopifyConnected(orgId: number): Promise<boolean> {
    try {
      // Try to fetch shop information - this will only work if the app is installed
      // and has proper access
      const response = await this.shopifyApiInvoker.invokeAdminApi(
        orgId,
        '/get-shop-info',  // This endpoint gets basic shop information
        'GET'
      );
      // If we get here without an error, the app is installed
      return !!response; // Ensure we return a boolean
    } catch (error) {
      console.log('App installation check failed:', error);
      return false; // If there's an error, assume the app is not installed
    }
  }

  private async fetchProductsFromShopify(orgId: number): Promise<any[]> {
    try {
      // Create a cache key specific to this organization
      const cacheKey = `shopify:products:${orgId}`;

      // Try to get cached products first
      const cachedProducts = this.productCache.get<any[]>(cacheKey);
      if (cachedProducts) {
        console.log(`Using cached products for org ${orgId}`);
        return cachedProducts;
      }

      // If not cached, fetch from Shopify
      console.log(`Fetching fresh products from Shopify for org ${orgId}`);
      const products = await this.adminUiController.getProducts(orgId);

      // Only cache if we got valid results
      if (products && products.length > 0) {
        // Cache for 10 minutes (TTL is already set in NodeCache initialization)
        this.productCache.set(cacheKey, products);
        console.log(`Cached ${products.length} products for org ${orgId}`);
      }

      return products;
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  }

  /**
   * Invalidate the product cache for a specific organization
   * Call this when products are updated in Shopify
   */
  public async invalidateProductCache(orgId: number): Promise<void> {
    const cacheKey = `shopify:products:${orgId}`;
    this.productCache.del(cacheKey);
    console.log(`Invalidated product cache for org ${orgId}`);
  }

  /**
   * Invalidate the metric cache for a specific organization
   * Call this when metrics are updated
   */
  public async invalidateMetricCache(orgId: number): Promise<void> {
    // Delete all cache keys that start with metrics:${orgId}:
    const keys = this.metricCache.keys();
    const orgKeys = keys.filter(key => key.startsWith(`metrics:${orgId}:`));
    orgKeys.forEach(key => this.metricCache.del(key));
    console.log(`Invalidated ${orgKeys.length} metric cache entries for org ${orgId}`);
  }

  /**
   * Enhanced search and ranking function using Fuse.js for fuzzy matching
   * This greatly reduces the number of manual Levenshtein calculations
   * and allows Fuse's highly optimized search algorithm to handle the
   * heavy lifting.
   */
  private searchAndRankProducts(products: any[], query: string): any[] {
    if (!query.trim()) return [];

    const fuse = new Fuse(products, {
      includeScore: true,
      threshold: 0.4,
      ignoreLocation: true,
      keys: [
        {name: 'title', weight: 0.5},
        {name: 'variants.title', weight: 0.2},
        {name: 'description', weight: 0.2},
        {name: 'tags', weight: 0.1},
      ],
    });

    return fuse.search(query).map(result => ({
      ...result.item,
      score: 1 - (result.score ?? 0),
    }));
  }
}
