import {injectable, BindingScope, inject} from '@loopback/core';
import {OrganizationPlan, OrganizationPlanRelations, PlanFeatureWithRelations} from '../models';
import {repository} from '@loopback/repository';
import {FeatureSettingRepository, OrganizationRepository, OrganizationPlanRepository, PlanFeatureRepository, PlanRepository, LoyaltyProgramRepository, PromotionalCampaignRepository, OrganizationMetricRepository, MetricRepository} from '../repositories';
import {PromotionalCampaignController} from '../controllers';

// const featureStateCache = new Map<number, any>();

@injectable({scope: BindingScope.TRANSIENT})
export class FeatureService {
	constructor(
		@repository(FeatureSettingRepository)
		public featureSettingRepository: FeatureSettingRepository,
		@repository(OrganizationRepository)
		public organizationRepository: OrganizationRepository,
		@repository(OrganizationPlanRepository)
		public organizationPlanRepository: OrganizationPlanRepository,
		@repository(OrganizationMetricRepository)
		public organizationMetricRepository: OrganizationMetricRepository,
		@repository(PlanFeatureRepository)
		public planFeatureRepository: PlanFeatureRepository,
		@repository(PlanRepository)
		public planRepository: PlanRepository,
		@repository(LoyaltyProgramRepository)
		public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(PromotionalCampaignRepository)
		public promotionalCampaignRepository: PromotionalCampaignRepository,
		@inject('controllers.PromotionalCampaignController')
		private promotionalCampaignController: PromotionalCampaignController,
		@repository(MetricRepository)
		public metricRepository: MetricRepository,
	) { }

	async getFeatureStates(
		orgId: number
	): Promise<{
		plan: any;
		currentOrgPlan: any;
		parentOrgPlan: any;
		features: (PlanFeatureWithRelations & {available: boolean, enabled: boolean, live: boolean})[]
	}> {
		// if (featureStateCache.has(orgId)) {
		// 	return featureStateCache.get(orgId);
		// }

		// Fetch the organization details
		const org = await this.organizationRepository.findById(orgId);

		// Get both current org and parent org plans
		const currentOrgPlan = await this.organizationPlanRepository.findOne({
			where: {orgId: orgId, status: 'ACTIVE'},
			include: [{relation: 'plan'}],
		}) as OrganizationPlan & OrganizationPlanRelations;

		let parentOrgPlan: (OrganizationPlan & OrganizationPlanRelations) | null = null;
		if (org.parentOrgId) {
			parentOrgPlan = await this.organizationPlanRepository.findOne({
				where: {orgId: org.parentOrgId, status: 'ACTIVE'},
				include: [{relation: 'plan'}],
			}) as OrganizationPlan & OrganizationPlanRelations;
		}

		// Determine which plan to use as primary (prefer parent org plan if it exists)
		const primaryPlan = parentOrgPlan?.plan || currentOrgPlan?.plan;

		if (!primaryPlan) {
			console.warn(`No active plan found for org ${orgId} or its parent org ${org.parentOrgId}`);
			return {
				plan: undefined,
				currentOrgPlan: currentOrgPlan?.plan || null,
				parentOrgPlan: parentOrgPlan?.plan || null,
				features: [],
			};
		}

		// Get plan features from both plans
		const primaryPlanFeatures = await this.planFeatureRepository.find({
			where: {planId: primaryPlan.id},
			include: [{relation: 'feature'}],
		});

		let currentPlanFeatures: any[] = [];
		if (currentOrgPlan?.plan && currentOrgPlan.plan.id !== primaryPlan.id) {
			currentPlanFeatures = await this.planFeatureRepository.find({
				where: {planId: currentOrgPlan.plan.id},
				include: [{relation: 'feature'}],
			});
		}

		// Find feature settings for the organization
		const featureSettings = await this.featureSettingRepository.find({
			where: {organizationId: orgId},
		});

		// Create a map of current org plan features for easy lookup
		const currentPlanFeatureMap = new Map();
		currentPlanFeatures.forEach(feature => {
			currentPlanFeatureMap.set(feature.featureId, feature);
		});

		// Merge plan features, choosing the best access for each feature
		const features = primaryPlanFeatures.map(primaryFeature => {
			const currentFeature = currentPlanFeatureMap.get(primaryFeature.featureId);

			// Choose the feature with better access
			let bestFeature = primaryFeature;
			if (currentFeature && this.hasFeatureBetterAccess(currentFeature, primaryFeature)) {
				bestFeature = currentFeature;
			}

			const featureSetting = featureSettings.find(setting => setting.name === bestFeature.featureId);
			return {
				...bestFeature,
				available: bestFeature?.enabled,
				enabled: bestFeature?.enabled && featureSetting?.enabled,
				live: featureSetting?.live,
			};
		}) as (PlanFeatureWithRelations & {available: boolean, enabled: boolean, live: boolean})[];

		// Add any features that exist only in current org plan
		currentPlanFeatures.forEach(currentFeature => {
			if (!primaryPlanFeatures.find(pf => pf.featureId === currentFeature.featureId)) {
				const featureSetting = featureSettings.find(setting => setting.name === currentFeature.featureId);
				features.push({
					...currentFeature,
					available: currentFeature?.enabled,
					enabled: currentFeature?.enabled && featureSetting?.enabled,
					live: featureSetting?.live,
				});
			}
		});

		return {
			plan: primaryPlan,
			currentOrgPlan: currentOrgPlan?.plan || null,
			parentOrgPlan: parentOrgPlan?.plan || null,
			features,
		};
	}

	/**
	 * Determines if feature A has better access than feature B
	 * Better access means: enabled > disabled, unlimited > limited, higher limit > lower limit
	 */
	private hasFeatureBetterAccess(featureA: any, featureB: any): boolean {
		// If one is enabled and the other isn't, enabled wins
		if (featureA.enabled && !featureB.enabled) return true;
		if (!featureA.enabled && featureB.enabled) return false;

		// If both are disabled, they're equivalent
		if (!featureA.enabled && !featureB.enabled) return false;

		// Both are enabled, check unlimited vs limited
		if (featureA.unlimited && !featureB.unlimited) return true;
		if (!featureA.unlimited && featureB.unlimited) return false;

		// If both are unlimited or both are limited, check limits
		if (!featureA.unlimited && !featureB.unlimited) {
			// Higher limit is better (undefined/null limit treated as 0)
			const limitA = featureA.limit || 0;
			const limitB = featureB.limit || 0;
			return limitA > limitB;
		}

		// If both are unlimited, they're equivalent
		return false;
	}

	async isFeatureAvailable(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return feature?.available ?? false;
	}

	async isFeatureEnabled(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return (feature?.available && feature?.enabled) ?? false;
	}

	async isFeatureLive(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return (feature?.available && feature?.live) ?? false;
	}

	async isFeatureLiveAndEnabled(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return (feature?.available && feature?.enabled && feature?.live) ?? false;
	}

	// async invalidateCache(orgId: number) {
	// 	featureStateCache.delete(orgId);
// 	featureStateCache.delete(orgId.toString() as any);
	// 	featureStateCache.delete(Number(orgId));
	// }

	async updateLoyaltyAndGwpEnabled(orgId: number) {
		await this.updateLoyaltyEnabled(orgId);
		await this.updateGwpEnabled(orgId);
		await this.updateMetrics(orgId);
	}

	async updateLoyaltyEnabled(orgId: number) {
		const enabled = await this.isFeatureAvailable('loyalty-app', orgId);
		console.log(`Setting loyalty to ${enabled} for org ${orgId}`);

		await this.setLoyaltyEnabled(orgId, enabled);
	}

	async updateGwpEnabled(orgId: number) {
		const enabled = await this.isFeatureAvailable('gwp-features', orgId);
		console.log(`Setting GWP to ${enabled} for org ${orgId}`);

		await this.setGwpEnabled(orgId, enabled);
	}

	async updateMetrics(orgId: number) {
		// Use the same logic as getFeatureStates to get the best features from both plans
		const featureStates = await this.getFeatureStates(orgId);

		if (!featureStates.plan) {
			console.warn(
				`No active plan found for organization ID: ${orgId}. Deleting all metrics for this organization.`,
			);
			// Delete all metrics if no plan exists
			await this.organizationMetricRepository.deleteAll({orgId});
			return;
		}

		// Get enabled features from the merged feature states
		const enabledFeatures = featureStates.features.filter(f => f.available);
		const featureIds = enabledFeatures.map(f => f.featureId);

		if (featureIds.length === 0) {
			// If there are no enabled features, delete all OrganizationMetrics
			await this.organizationMetricRepository.deleteAll({orgId});
			return;
		}

		// Fetch all Metrics associated with these features
		const metrics = await this.metricRepository.find({
			where: {featureId: {inq: featureIds}, isDefault: true},
		});

		const metricIds = metrics.map(m => m.id!);

		// Fetch existing OrganizationMetrics for this org and these metricIds
		const existingOrgMetrics = await this.organizationMetricRepository.find({
			where: {
				orgId,
				metricId: {inq: metricIds},
			},
		});

		// Create a Set of existing metricIds
		const existingMetricIds = new Set(existingOrgMetrics.map(om => om.metricId));

		// Find metrics that need to be created
		const metricsToCreate = metrics.filter(metric => !existingMetricIds.has(metric.id!));

		// Prepare new OrganizationMetrics to create
		const newOrgMetrics = metricsToCreate.map(metric => ({
			orgId,
			metricId: metric.id!,
			runFrequency: metric.defaultRunFrequency,
			// Add other properties as needed
		}));

		// Create new OrganizationMetrics in bulk
		if (newOrgMetrics.length > 0) {
			await this.organizationMetricRepository.createAll(newOrgMetrics);
		}

		// Delete OrganizationMetrics that are no longer associated with the plan
		await this.organizationMetricRepository.deleteAll({
			orgId,
			metricId: {nin: metricIds},
		});
	}

	private async setLoyaltyEnabled(orgId: number, active: boolean) {
		if (active) {
			return;
		}

		const program = await this.loyaltyProgramRepository.findOne({
			where: {orgId},
		});

		if (program) {
			await this.loyaltyProgramRepository.updateById(program.id, {active});
		}
	}

	private async setGwpEnabled(orgId: number, active: boolean) {
		if (active) {
			return;
		}


		const campaigns = await this.promotionalCampaignRepository.find({
			where: {orgId},
		});

		console.log('Campaigns', campaigns);

		for (const campaign of campaigns) {
			// await this.promotionalCampaignRepository.updateById(campaign.id, { active });
			try {
				console.log('Deleting free gift for campaign', campaign.id);
				await this.promotionalCampaignController.deleteFreeGift({id: campaign.id}, campaign.id!, orgId);
			} catch (e) {
				console.error('Error deleting free gift for campaign', campaign.id, e);
			}
		}
	}
}
