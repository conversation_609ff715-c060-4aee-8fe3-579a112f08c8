import {createRouter, createWebHistory} from 'vue-router';
import * as Utils from '../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;
import Test from './pages/Test.ts.vue';
import Home from './pages/Home.ts.vue';
import OnboardTest from './pages/OnboardTest.ts.vue';
import Campaigns from './pages/Campaigns.ts.vue';
import LoyaltyProgram from './pages/LoyaltyProgram.ts.vue';
import Charts from './pages/Charts.ts.vue';
import CampaignDetail from './pages/CampaignDetail.ts.vue';
import CampaignWayToEarnAdd from './pages/CampaignWayToEarnAdd.ts.vue';
import LoyaltyBranding from './pages/LoyaltyBranding.ts.vue';
import LoyaltyBrandingReferralProgram from './pages/LoyaltyBrandingReferralProgram.ts.vue';
import Shoppers from './pages/Shoppers.ts.vue';
import ShopperDetails from './pages/subpages/Shopper-Details.ts.vue';
import LoyaltyBrandingNotifications from './pages/LoyaltyBrandingNotifications.ts.vue';
import LoyaltyBrandingCheckout from './pages/LoyaltyBrandingCheckout.ts.vue';
import Analytics from './pages/Analytics.ts.vue';
import AnalyticsDashboard from './pages/AnalyticsDashboard.ts.vue';
import Chat from './pages/Chat.ts.vue'; // Added Chat component import
import Signin from './pages/Signin.ts.vue';
import OnboardingStart from './pages/onboarding/page1.ts.vue';
import OnboardingLVO from './pages/onboarding/LVO.ts.vue';
//import Account from './pages/Users.ts.vue';
import PageNotFound from './pages/utility/PageNotFound.vue';
import Account from '../client-old/pages/settings/Users.vue';
import Users from '../client-old/pages/settings/Users.vue';
import AccountSettings from '../client-old/pages/settings/Account.vue';
import MyAccount from '../client-old/pages/settings/MyAccount.vue';
import ResetPassword from '../client-old/pages/ResetPassword.vue';
import Signup from '../client-old/pages/Signup.vue';
import DocSignin from '../client-old/pages/DocSignin.vue';
import ShopifyExtension from './components/ShopifyExtensions.ts.vue';
import ProgramSettings from './components/ProgramSettings.ts.vue';
import DataSettings from './components/DataSettings.ts.vue';
import ExperimentalSettings from './components/ExperimentalSettings.ts.vue';
import AISettings from './components/AISettings.ts.vue';
import CurrencySettings from './components/CurrencySettings.ts.vue';
import Integrations from './pages/Integrations.ts.vue';
import Quickstart from './pages/Quickstart.ts.vue';
import MemberInsights from './pages/MemberInsights.ts.vue';
import VIPTierInsights from './pages/VIPTierInsights.ts.vue';
import LoyaltyPerformance from './pages/LoyaltyPerformance.ts.vue';
import ReferralAnalytics from './pages/ReferralAnalytics.ts.vue';
import VIPAdd from './pages/VIPAdd.vue';
import VIPDetails from './pages/VIPDetails.ts.vue';
import Giveaways from './pages/Giveaways.ts.vue';
import GiveawayDetails from './pages/GiveawayDetails.ts.vue';
// import GiveawayWTEAdd from './pages/GiveawayWTEAdd.ts.vue';
import AddWayToEarn from './pages/AddWayToEarn.ts.vue';
import Referrals from './pages/Referrals.ts.vue';
import Plans from './pages/Plans.ts.vue';
import Support from './pages/Support.ts.vue';
import ReceiptReview from './pages/CustomerReview.ts.vue';
import OnsiteDisplay from './pages/OnsiteDisplay.ts.vue';
import LoyaltyEmails from './pages/LoyaltyEmails.ts.vue';
import EmailConfiguration from './pages/EmailConfiguration.ts.vue';
import Unsubscribe from './pages/Unsubscribe.ts.vue';
import Translations from './pages/Translations.ts.vue';
import AdvancedCampaign from './pages/AdvancedCampaignDetail.ts.vue';
import CollabMonetize from './pages/CollabMonetize.ts.vue';
import CollabGrow from './pages/CollabGrow.ts.vue';
import SmartOfferCampaign from './pages/SmartOfferCampaign.ts.vue';
import VIPTierInsightsTs from './pages/VIPTierInsights.ts.vue';
import GiftWithPurchase from './pages/GiftWithPurchase.ts.vue';
import GWPCampaign from './pages/GWPCampaign.ts.vue';
import EditMenuNavigationTs from './pages/subpages/EditMenuNavigation.ts.vue';
import PromoBox from './pages/subpages/PromoBox.ts.vue';
import SegmentOverview from './pages/SegmentsOverview.ts.vue';
import SegmentView from './pages/SegmentView.ts.vue';
import SignalBook from './pages/SignalBook.ts.vue';
import AgentPlanner from './pages/AgentPlanning.ts.vue';
import AgentTask from './pages/AgentTask.ts.vue';
import AgentPlan from './pages/AgentPlan.ts.vue';
import AgentKnowledge from './pages/AgentKnowledge.ts.vue';
import AgentTaskList from './pages/AgentTaskList.ts.vue';
import LandingPage from './pages/subpages/LandingPage.ts.vue'
import RetentionInsights from './pages/RetentionInsights.ts.vue';
import AIAnalyst from './pages/AIAnalyst.ts.vue';
import SignupNew from './pages/SignUp.vue'

import SegmentOverviewX from './pages/SegmentsOverviewX.ts.vue';
import SegmentViewX from './pages/SegmentViewX.ts.vue';
import PromptManager from './pages/PromptManager.ts.vue';
import ChatView from './pages/ChatView.vue';
import HistoricChats from './pages/HistoricChats.ts.vue';
import Agency from './pages/Agency.ts.vue';

const routerHistory = createWebHistory()

const router = createRouter({
	history: routerHistory,
	scrollBehavior(to, from, savedPosition) {
		//attempt to always scroll to top of page on route change
		return { top: 0 };
	},
	routes: [
		{
			path: '/loyalty/customers',
			component: Shoppers
		},
		{
			path: '/prompt-manager',
			component: PromptManager
		},
		{
			path: '/customer/:id',
			name: 'shopper-details',
			component: ShopperDetails,
			props: true
		},
		{
			path: '/',
			component: Signin
		},
		{
			path: '/loyalty/home',
			component: Home
		},
		{
			path: '/chat', // Individual chat view
			component: Chat
		},
		{
			path: '/chats', // Historic chats list view
			component: HistoricChats
		},
		{
			path: '/ai-strategist/analyst',
			component: AIAnalyst
		},
		{
			path: '/onboarding/start',
			component: OnboardingLVO
		},
		{
			path: '/onboarding/lvo',
			component: OnboardingLVO
		},
		{
			path: '/loyalty/campaigns',
			component: Campaigns
		},
		{
			path: '/loyalty/program',
			component: LoyaltyProgram
		},
		{
			path: '/loyalty/edit-menu-navigation',
			component: EditMenuNavigationTs
		},
		{
			path: '/loyalty/promo-box',
			component: PromoBox
		},
		{
			path: '/loyalty/landing-page',
			component: LandingPage
		},
		{
			path: '/test',
			component: Test
		},
		{
			path: '/onboard-test',
			component: OnboardTest
		},
		{
			path: '/loyalty/quickstart',
			component: Quickstart
		},
		{
			path: '/loyalty/program/vip/add',
			component: VIPAdd
		},
		{
			path: '/loyalty/program/vip',
			component: VIPDetails
		},
		{
			path: '/loyalty/program/referrals',
			component: Referrals
		},
		{
			path: '/promotions/gift-with-purchase',
			component: GiftWithPurchase
		},
		{
			path: '/ai-segments/segments/true',
			component: SegmentOverview,
		},
		{
			path: '/ai-segments/overview',
			component: SegmentOverview,
		},
		// {
		// 	path: '/promotions/segments/abc',
		// 	component: SegmentView
		// },
		{
			path: '/ai-segments/segments/:id',
			component: SegmentView,
			props: true
		},
		{
			path: '/ai-segments/segmentsx/:loading',
			component: SegmentOverviewX,
			props: true,
		},
		{
			path: '/ai-segments/segmentsx/abc',
			component: SegmentViewX
		},
		{
			path: '/ai-segments/signalbook',
			component: SignalBook
		},
		{
			path: '/ai-strategist/knowledge',
			component: AgentKnowledge
		},
		{
			path: '/ai-segments/knowledge',
			component: AgentKnowledge
		},
		{
			path: '/ai-strategist/planning',
			component: AgentPlanner
		},
		{
			path: '/ai-strategist/tasks',
			component: AgentTaskList
		},
		{
			path: '/promotions/workspace',
			component: AgentTask
		},
		{
			path: '/ai-strategist/tasks/:taskId',
			component: AgentTask
		},
		{
			path: '/ai-strategist/planning/plan/:planId',
			component: AgentPlan
		},
		{
			path: '/ai-strategist/analytics',
			component: Analytics,
			props: () => ({
				isStrategist: true
			})
		},
		{
			path: '/loyalty/program/giveaways',
			component: Giveaways
		},
		{
			path: '/loyalty/program/giveaway/:giveawayId',
			component: GiveawayDetails,
			props: true
		},
		{
			path: '/loyalty/settings/plans',
			component: Plans
		},
		{
			path: '/collabmonetize',
			component: CollabMonetize
		},
		{
			path: '/collabgrow',
			component: CollabGrow
		},
		{
			path: '/support',
			component: Support
		},
		{
			path: '/offers',
			component: SmartOfferCampaign
		},
		{
			path: '/gwpCampaign',
			component: GWPCampaign
		},
		{
			path: '/advcampaign/:campaignId',
			component: AdvancedCampaign,
			props: true
		},
		{
			path: '/receiptreview',
			component: ReceiptReview
		},
		{
			path: '/onsite',
			component: OnsiteDisplay
		},
		{
			path: '/loyalty/campaign/:campaignId/:abtest?',
			component: CampaignDetail,
			props: true
		},
		{
			path: '/campaign/:campaignId/ways-to-earn/add',
			component: CampaignWayToEarnAdd,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		  },
		{
			path: '/campaign/:campaignId/new-ways-to-earn/add',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/new-ways-to-earn/edit/:wayToEarnId',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/ways-to-earn/edit/:wayToEarnId',
			component: CampaignWayToEarnAdd,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			}),
			name: 'EditWayToEarn'
		},
		{
			path: '/campaign/:campaignId/points/add',
			component: CampaignWayToEarnAdd,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		  },
		{
			path: '/campaign/:campaignId/new-points/add',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/new-points/edit/:wayToEarnId',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/points/edit/:wayToEarnId',
			component: CampaignWayToEarnAdd,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			}),
			name: 'EditWayToEarn'
		},
		{
			path: '/campaign/:campaignId/shop-reward/edit/:wayToEarnId',
			component: CampaignWayToEarnAdd,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			}),
			name: 'EditShopReward'
		},
		{
			path: '/campaign/:campaignId/shop-reward/add',
			component: CampaignWayToEarnAdd,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/new-shop-reward/edit/:wayToEarnId',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			}),
			name: 'EditShopReward'
		},
		{
			path: '/campaign/:campaignId/new-shop-reward/add',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/new-perk-reward/edit/:wayToEarnId',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational
			}),
			name: 'EditPerkReward'
		},
		{
			path: '/campaign/:campaignId/new-perk-reward/add',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational
			})
		},
		{
			path: '/campaign/:campaignId/new-giveaway-wte/add',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				isFoundationalCampaign: route.query.foundational,
				giveawayId: route.query.giveawayId
			})
		},
		{
			path: '/campaign/:campaignId/new-giveaway-wte/edit/:wayToEarnId',
			component: AddWayToEarn,
			props: route => ({
				campaignId: route.params.campaignId,
				wayToEarnId: route.params.wayToEarnId,
				isFoundationalCampaign: route.query.foundational,
				giveawayId: route.query.giveawayId,
			})
		},
		{
			path: '/loyalty/onsite',
			component: OnsiteDisplay
		},
		{
			path: '/loyalty/branding',
			component: LoyaltyBranding
		},
		{
			path: '/loyalty/referral-program',
			component: LoyaltyBrandingReferralProgram
		},
		{
			path: '/loyalty/notifications',
			component: LoyaltyBrandingNotifications
		},
		{
			path: '/loyalty/checkout',
			component: LoyaltyBrandingCheckout
		},
		{
			path: '/loyalty/analytics',
			component: Analytics
		},
		{
			path: '/loyalty/communication',
			component: LoyaltyEmails
		},
		{
			path: '/loyalty/email',
			component: EmailConfiguration,
			props: route => ({
				eventName: route.query.eventName,
			})
		},
		{
			path: '/unsubscribe/:customerId',
			component: Unsubscribe,
			props: route => ({
				customerId: route.params.customerId,
			})
		},
		{
			path: '/loyalty/analytics/member-insights',
			component: MemberInsights
		},
		{
			path: '/ai-strategist/analytics/member-insights',
			component: MemberInsights
		},
		{
			path: '/loyalty/analytics/vip-insights',
			component: VIPTierInsights
		},
		{
			path: '/loyalty/analytics/loyalty-performance',
			component: LoyaltyPerformance
		},
		{
			path: '/loyalty/analytics/referral-analytics',
			component: ReferralAnalytics
		},
		{
			path: '/ai-strategist/analytics/retention-insights',
			component: RetentionInsights
		},
		{
			path: '/loyalty/integrations',
			component: Integrations
		},
		{
			path: '/ai-strategist/integrations',
			component: Integrations
		},
		{
			path: '/ai-segments/integrations',
			component: Integrations
		},
		{
			path: '/integrations',
			component: Integrations
		},
		{
			path: '/analytics/:dashboardId',
			component: AnalyticsDashboard,
			props: true
		},
		{
			path: '/charts/:dashboardId',
			component: Charts,
			props: true
		},
                {
                        path: '/loyalty/settings/manage-users',
                        component: Users
                },
                {
                        path: '/loyalty/settings/data',
                        component: DataSettings
                },
                {
                        path: '/loyalty/settings/experimental',
                        component: ExperimentalSettings
                },
                {
                        path: '/loyalty/settings/account',
                        component: AccountSettings
                },
                {
                        path: '/loyalty/settings/myprofile',
                        component: MyAccount
                },
                {
                        path: '/settings/loyalty',
                        component: ProgramSettings
                },
                {
                        path: '/settings',
                        component: MyAccount
                },
                {
                        path: '/loyalty/settings/shopify-extensions',
                        component: ShopifyExtension
                },
		{
			path: '/loyalty/settings/manage-currencies',
			component: CurrencySettings
		},
		{
			path: '/loyalty/settings/ai-settings',
			component: AISettings
		},
		{
				path: '/loyalty/settings/translations',
				component: Translations
		},
		{
				path: '/agency',
				component: Agency
		},
		{
				path: '/invite',
				component: Signup
		},
		{
				path: '/signup',
				component: SignupNew
		},
		{
			path: '/signin',
			component: Signin,
			name: 'signin'
		},
		{
			path: '/docsignin',
			component: DocSignin
		},
		{
			path: '/reset-password',
			component: ResetPassword
		},
		{
			path: '/ai-strategist/chat',
			component: ChatView
		},
		{
			path: '/:pathMatch(.*)*',
			component: PageNotFound
		}
	]
});

const unauthenticatedRoutes = [
	'/invite',
	'/signin',
	'/signup',
	'/reset-password',
	'/reset-password-confirm',
	'/utility/oauth-redirect',
	'/set-cookie',
];

router.beforeEach(async (to, from) => {
	if (!unauthenticatedRoutes.includes(to.path)) {
		const isLoggedIn = !!localStorage.getItem('token');

		if (!isLoggedIn && (to.path !== '/' && to.path !== '/login' && to.path !== '/signin' && to.path !== '/docsignin')) {
			return { name: 'signin'}
		}

		if (isLoggedIn && (to.query.session_id || to.query.source)) {
			const params = {
                session_id: to.query.session_id,
                redirect: to.query.redirect || undefined,
				source: to.query.source || undefined,
            };
			return { name: 'signin', query: params};
		}

		// Redirect after authentication based on organization type
		if (isLoggedIn && (to.path === '/' || to.path === '/signin')) {
			try {
				const token = localStorage.getItem('token');
				const orgId = localStorage.getItem('userOrgId');

				if (token && orgId) {
					// Fetch organization details to check if it's an agency
					const response = await fetch(`${URL_DOMAIN}/organizations/${orgId}`, {
						method: 'GET',
						headers: {
							Authorization: `Bearer ${token}`,
							'Content-Type': 'application/json',
						},
					});

					if (response.ok) {
						const orgData = await response.json();
						// Redirect agency organizations to /agency, others to /chat
						if (orgData.orgType === 'agency') {
							return { path: '/agency' };
						}
					}
				}
			} catch (error) {
				console.error('Error fetching organization details for redirect:', error);
			}

			// Default redirect to chat for brand organizations or if fetch fails
			return { path: '/chat' };
		}
	}
	//workaround for vue router bug
	window.history.pushState({ current: from.fullPath }, '', to.fullPath);
	return true;
});



export default router;
