<template>
  <div class="flex h-screen overflow-hidden bg-gray-50">
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
          <div class="mb-8 flex">
            <h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Experimental Settings</h1>
          </div>
          <div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
            <div class="flex flex-col md:flex-row md:-mr-px">
              <SettingsSidebar />
              <div class="grow">
                <div class="p-8 space-y-8">
                  <div class="mb-6">
                    <p class="text-gray-600 leading-relaxed">These are experimental features in rapid development. They will improve over time as we refine and enhance their capabilities.</p>
                  </div>

                  <!-- Email Generation Feature ---->
                  <section class="border border-gray-200 rounded-xl p-6 bg-gradient-to-r from-purple-50 to-blue-50">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex-1">
                        <div class="flex items-center mb-2">
                          <svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                          </svg>
                          <h3 class="text-lg font-semibold text-gray-900">Email Generation</h3>
                          <span class="ml-3 px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Ready</span>
                        </div>
                        <p class="text-gray-600 leading-relaxed mb-3">
                          This enables AI-powered email generation through our Brief Chat system. The feature allows you to create professional emails with AI assistance.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                          <div class="flex">
                            <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div class="text-sm text-blue-700">
                              <strong>Requirements:</strong> You'll need to upload brand images for the Email Generation to use. This feature is good but requires some brand configuration. You can reach out to Raleon for help with setup.
                            </div>
                          </div>
                        </div>
                        
                        <!-- Setup Checklist -->
                        <div v-if="emailGenerationEnabled" class="bg-white border border-gray-200 rounded-lg p-4 mt-4">
                          <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-semibold text-gray-900 flex items-center">
                              <svg class="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                              </svg>
                              Setup Checklist
                            </h4>
                            <button @click="loadChecklistData()" :disabled="loadingChecklist" class="text-xs text-purple-600 hover:text-purple-800 flex items-center">
                              <svg v-if="loadingChecklist" class="w-3 h-3 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                              </svg>
                              <svg v-else class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                              </svg>
                              {{ loadingChecklist ? 'Checking...' : 'Refresh' }}
                            </button>
                          </div>
                          
                          <div class="space-y-3">
                            <!-- Step 1: Upload Hero Images -->
                            <div class="flex items-start space-x-3">
                              <div class="flex-shrink-0 mt-0.5">
                                <div :class="[
                                  'w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium',
                                  hasHeroImages ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                                ]">
                                  <svg v-if="hasHeroImages" class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                  </svg>
                                  <span v-else>1</span>
                                </div>
                              </div>
                              <div class="flex-1">
                                <div class="flex items-center justify-between">
                                  <span :class="['text-sm font-medium', hasHeroImages ? 'text-green-800' : 'text-gray-900']">
                                    Upload Hero Images as reference
                                  </span>
                                  <a href="/ai-strategist/knowledge?tab=assets" target="_blank" rel="noopener noreferrer"
                                    class="text-xs text-purple-600 hover:text-purple-800 font-medium flex items-center">
                                    Upload Images
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M14 4h6m0 0v6m0-6L10 14"/>
                                    </svg>
                                  </a>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">
                                  Add images with the "Hero Image" tag for AI to reference in email generation
                                </p>
                              </div>
                            </div>
                            
                            <!-- Step 2: Customize Components (Optional) -->
                            <div class="flex items-start space-x-3">
                              <div class="flex-shrink-0 mt-0.5">
                                <div :class="[
                                  'w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium',
                                  hasCustomComponents ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-600'
                                ]">
                                  <svg v-if="hasCustomComponents" class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                  </svg>
                                  <span v-else>2</span>
                                </div>
                              </div>
                              <div class="flex-1">
                                <div class="flex items-center justify-between">
                                  <span class="text-sm font-medium text-gray-900 flex items-center">
                                    Customize your components
                                    <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Optional</span>
                                  </span>
                                  <div class="flex space-x-2">
                                    <a href="/ai-strategist/knowledge?tab=emailComponents" target="_blank" rel="noopener noreferrer"
                                      class="text-xs text-purple-600 hover:text-purple-800 font-medium flex items-center">
                                      Email Sections
                                      <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M14 4h6m0 0v6m0-6L10 14"/>
                                      </svg>
                                    </a>
                                  </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">
                                  Create custom email sections for more personalized designs
                                </p>
                                <div class="flex space-x-4 mt-2">
                                  <a href="#" class="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Documentation
                                  </a>
                                  <a href="#" class="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                    Video Tutorial
                                  </a>
                                </div>
                              </div>
                            </div>
                            
                            <!-- Step 3: Enjoy the Magic -->
                            <div class="flex items-start space-x-3">
                              <div class="flex-shrink-0 mt-0.5">
                                <div class="w-5 h-5 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center text-xs font-medium">
                                  3
                                </div>
                              </div>
                              <div class="flex-1">
                                <span class="text-sm font-medium text-gray-900 flex items-center">
                                  Enjoy the magic!
                                  <span class="ml-2">✨</span>
                                </span>
                                <p class="text-xs text-gray-500 mt-1">
                                  Your AI is now ready to generate personalized emails using your brand assets
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="ml-6">
                        <label class="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" v-model="emailGenerationEnabled" @change="toggleEmailGeneration" :disabled="loading" class="sr-only peer">
                          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"></div>
                        </label>
                      </div>
                    </div>
                  </section>

                  <!-- Remote Email Agent Feature ---->
                  <section class="border border-gray-200 rounded-xl p-6 bg-gradient-to-r from-orange-50 to-red-50">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex-1">
                        <div class="flex items-center mb-2">
                          <svg class="w-6 h-6 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                          </svg>
                          <h3 class="text-lg font-semibold text-gray-900">Remote Email Agent</h3>
                          <span class="ml-3 px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full">Early Access</span>
                        </div>
                        <p class="text-gray-600 leading-relaxed mb-3">
                          This enables the ability in Brief Chat to see a new tab for Design Jobs that allows you to run longer running background jobs to create emails and then bring them back into the chat.
                        </p>
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                          <div class="flex">
                            <svg class="w-5 h-5 text-orange-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.99-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                            <div class="text-sm text-orange-700">
                              <strong>Warning:</strong> This feature is really early and not ready for production. Enable only for testing purposes.
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="ml-6">
                        <label class="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" v-model="remoteEmailAgentEnabled" @change="toggleRemoteEmailAgent" :disabled="loading" class="sr-only peer">
                          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"></div>
                        </label>
                      </div>
                    </div>
                  </section>
                </div>
                <footer class="border-t border-gray-100 bg-gray-50/50 rounded-b-2xl">
                  <div class="flex flex-col px-8 py-6">
                    <div class="flex justify-end gap-4">
                      <button class="px-6 py-3 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
                        Cancel
                      </button>
                      <button 
                        @click="saveExperimentalSettings()"
                        :disabled="loading"
                        class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                      >
                        <span v-if="loading" class="w-4 h-4 mr-2">
                          <i class="fa fa-spinner fa-spin"></i>
                        </span>
                        {{ loading ? 'Saving...' : 'Save Changes' }}
                      </button>
                    </div>
                  </div>
                </footer>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  <StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
</template>

<script>
import SettingsSidebar from '../../client-old/partials/settings/SettingsSidebar.vue'
import StatusMessage from '../components/StatusMessage.ts.vue'
import * as Utils from '../../client-old/utils/Utils'
import * as OrganizationSettings from '../services/organization-settings.js'

const URL_DOMAIN = Utils.URL_DOMAIN

export default {
  name: 'ExperimentalSettings',
  components: { SettingsSidebar, StatusMessage },
  async mounted() {
    await this.loadExperimentalSettings()
  },
  data() {
    return {
      emailGenerationEnabled: false,
      remoteEmailAgentEnabled: false,
      status: { message: '', type: 'nope' },
      loading: false,
      hasHeroImages: false,
      hasCustomComponents: false,
      loadingChecklist: false
    }
  },
  methods: {
    async loadExperimentalSettings() {
      try {
        this.loading = true
        
        // Load the same settings as used in AgentKnowledge
        this.emailGenerationEnabled = (await OrganizationSettings.getOrganizationSetting('hasEmailGeneration')) === 'true'
        this.remoteEmailAgentEnabled = (await OrganizationSettings.getOrganizationSetting('hasNextGenEmail')) === 'true'
        
        // Load checklist data if email generation is enabled
        if (this.emailGenerationEnabled) {
          await this.loadChecklistData()
        }
        
      } catch (err) {
        console.error('Error loading experimental settings:', err)
        // Don't show error for loading, just use defaults
      } finally {
        this.loading = false
      }
    },
    
    async toggleEmailGeneration() {
      await this.saveExperimentalSettings()
      
      // Load checklist data when enabling email generation
      if (this.emailGenerationEnabled) {
        await this.loadChecklistData()
      }
    },
    
    async toggleRemoteEmailAgent() {
      await this.saveExperimentalSettings()
    },
    
    async loadChecklistData() {
      try {
        this.loadingChecklist = true
        
        // Check for Hero Images - fetch brand images and check for Hero Image type
        const response = await fetch(`${URL_DOMAIN}/branding/images?assetType=email`, {
          method: 'GET',
          credentials: 'omit',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const images = await response.json()
          console.log('All brand images:', images)
          console.log('Images with Hero Image type:', images.filter(img => 
            img.imageType === 'Hero Image' || img.contentType === 'Hero Image'
          ))
          
          // Check for Hero Images with multiple possible field names
          this.hasHeroImages = images.some(img => {
            const hasHeroType = img.imageType === 'Hero Image' || 
                               img.contentType === 'Hero Image' ||
                               img.category === 'Hero Image' ||
                               img.type === 'Hero Image'
            console.log(`Image ${img.filename || img.name || img.id}: imageType="${img.imageType}", contentType="${img.contentType}", category="${img.category}", type="${img.type}", hasHero=${hasHeroType}`)
            return hasHeroType
          })
          
          console.log('Final hasHeroImages result:', this.hasHeroImages)
        } else {
          console.error('Failed to fetch brand images:', response.status, response.statusText)
        }
        
        // Check for custom components
        this.hasCustomComponents = (await OrganizationSettings.getOrganizationSetting('hasCustomComponents')) === 'true'
        
      } catch (err) {
        console.error('Error loading checklist data:', err)
      } finally {
        this.loadingChecklist = false
      }
    },
    
    async saveExperimentalSettings() {
      try {
        this.loading = true
        this.status.type = 'info'
        this.status.message = 'Saving experimental settings...'
        
        // Save using the same organization settings as AgentKnowledge
        await OrganizationSettings.updateOrganizationSetting('hasEmailGeneration', String(this.emailGenerationEnabled))
        await OrganizationSettings.updateOrganizationSetting('hasNextGenEmail', String(this.remoteEmailAgentEnabled))
        
        this.status.type = 'success'
        this.status.message = 'Experimental settings saved successfully.'
        
      } catch (err) {
        console.error('Error saving experimental settings:', err)
        this.status.type = 'fail'
        this.status.message = 'Failed to save experimental settings'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
</style>