<template>
  <div
    v-if="show"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="$emit('close')"
  >
    <div class="bg-white rounded-xl shadow-xl w-full max-w-6xl mx-4 h-[95vh] overflow-hidden flex flex-col">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Edit Image</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Main Content Area -->
      <div class="flex flex-1 overflow-hidden">
        <!-- Left Panel -->
        <div class="w-[28rem] border-r bg-gray-50 overflow-y-auto flex flex-col">
          <!-- Mode Toggle -->
          <div class="p-6 border-b border-gray-200">
            <div class="text-sm font-medium text-gray-700 mb-3">What changes would you like to make to the selected image?</div>
            <div class="grid grid-cols-5 gap-1">
              <button
                @click="editMode = 'variation'"
                :class="[
                  'aspect-square px-1 py-1 text-xs font-medium rounded-md transition-all duration-200 flex flex-col items-center justify-center',
                  editMode === 'variation'
                    ? 'bg-purple-50 text-purple-700 shadow-sm border-2 border-purple-200'
                    : 'bg-white text-gray-600 hover:text-purple-600 hover:bg-purple-50 border-2 border-gray-200 hover:border-purple-200'
                ]"
              >
                <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="text-center leading-tight">Create Variation</span>
              </button>
              <button
                @click="editMode = 'text'"
                :class="[
                  'aspect-square px-1 py-1 text-xs font-medium rounded-md transition-all duration-200 flex flex-col items-center justify-center',
                  editMode === 'text'
                    ? 'bg-purple-50 text-purple-700 shadow-sm border-2 border-purple-200'
                    : 'bg-white text-gray-600 hover:text-purple-600 hover:bg-purple-50 border-2 border-gray-200 hover:border-purple-200'
                ]"
              >
                <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                <span class="text-center leading-tight">Edit Text</span>
              </button>
              <button
                @click="editMode = 'switch'"
                :class="[
                  'aspect-square px-1 py-1 text-xs font-medium rounded-md transition-all duration-200 flex flex-col items-center justify-center',
                  editMode === 'switch'
                    ? 'bg-purple-50 text-purple-700 shadow-sm border-2 border-purple-200'
                    : 'bg-white text-gray-600 hover:text-purple-600 hover:bg-purple-50 border-2 border-gray-200 hover:border-purple-200'
                ]"
              >
                <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
                <span class="text-center leading-tight">Switch Image</span>
              </button>
              <button
                @click="editMode = 'remove'"
                :class="[
                  'aspect-square px-1 py-1 text-xs font-medium rounded-md transition-all duration-200 flex flex-col items-center justify-center',
                  editMode === 'remove'
                    ? 'bg-purple-50 text-purple-700 shadow-sm border-2 border-purple-200'
                    : 'bg-white text-gray-600 hover:text-purple-600 hover:bg-purple-50 border-2 border-gray-200 hover:border-purple-200'
                ]"
              >
                <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span class="text-center leading-tight">Remove Text</span>
              </button>
              <button
                @click="editMode = 'addText'"
                :class="[
                  'aspect-square px-1 py-1 text-xs font-medium rounded-md transition-all duration-200 flex flex-col items-center justify-center',
                  editMode === 'addText'
                    ? 'bg-purple-50 text-purple-700 shadow-sm border-2 border-purple-200'
                    : 'bg-white text-gray-600 hover:text-purple-600 hover:bg-purple-50 border-2 border-gray-200 hover:border-purple-200'
                ]"
              >
                <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span class="text-center leading-tight">Add Text</span>
              </button>
            </div>

            <!-- Brief content for Add Text mode -->
            <div v-if="editMode === 'addText'" class="mt-4 space-y-3">
              <div class="bg-blue-50 rounded-lg border border-blue-200 p-3">
                <div class="text-xs text-blue-800">
                  <div class="font-medium mb-1">💡 Navigation Tips:</div>
                  <div class="space-y-1">
                    <div>• Hold <strong>Spacebar</strong> when zoomed in to pan around (cursor becomes a hand)</div>
                    <div>• <strong>Middle mouse wheel</strong> to zoom in/out</div>
                  </div>
                </div>
              </div>

            </div>
          </div>

          <!-- Left Panel Content -->
          <div class="p-6">
        <!-- Create Variation Mode -->
        <div v-if="editMode === 'variation'">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Describe the changes you want to make:
          </label>
          <textarea
            v-model="variationDescription"
            placeholder="e.g., Make the image brighter, change the background color to blue, add more contrast..."
            class="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
          ></textarea>
        </div>

        <!-- Switch Image Mode -->
        <div v-else-if="editMode === 'switch'">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Describe the type of image you'd like instead:
          </label>
          <textarea
            v-model="switchImageDescription"
            placeholder="e.g., a professional headshot instead, a nature landscape, a product photo on white background..."
            class="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
          ></textarea>
        </div>

        <!-- Edit Text Mode -->
        <div v-else-if="editMode === 'text'">
          <!-- Collapsible Detect Text Section -->
          <div class="mb-4">
            <button
              @click="toggleDetectedTextSection"
              class="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
            >
              <div class="flex items-center space-x-2">
                <span>AI Detected Text in Image</span>
                <div v-if="isDetectingText" class="flex items-center space-x-1 text-blue-600">
                  <svg class="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-xs">Detecting text...</span>
                </div>
                <div v-else-if="detectedTextElements.length > 0" class="inline-flex items-center px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
                  {{ detectedTextElements.length }} found
                </div>
                <div v-else-if="hasDetectedText" class="inline-flex items-center px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                  None found
                </div>
              </div>
              <svg
                :class="['h-4 w-4 transition-transform duration-200', showDetectedTextSection ? 'rotate-180' : '']"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Collapsible Content -->
            <div
              v-show="showDetectedTextSection"
              class="mt-3 transition-all duration-300 ease-in-out overflow-hidden"
            >
              <!-- Detecting State -->
              <div v-if="isDetectingText" class="p-4 bg-blue-50 rounded-lg border border-blue-200 text-center">
                <div class="flex items-center justify-center space-x-2 text-blue-700">
                  <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="font-medium">Analyzing image for text...</span>
                </div>
                <p class="text-sm text-blue-600 mt-1">This usually takes a few seconds</p>
              </div>

              <!-- Detected Text Options -->
              <div v-else-if="detectedTextElements.length > 0">
                <p class="text-xs text-gray-600 mb-2">Click any text to auto-fill an edit field:</p>
                <div class="flex flex-wrap gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <button
                    v-for="(element, index) in detectedTextElements"
                    :key="`detected-${index}`"
                    @click="selectDetectedText(element.text)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium bg-white border border-blue-300 text-blue-800 rounded-full hover:bg-blue-100 transition-colors duration-150"
                    :title="`${element.description} (${element.confidence} confidence)`"
                  >
                    "{{ element.text }}"
                  </button>
                </div>
              </div>

              <!-- No Text Found Message -->
              <div v-else-if="hasDetectedText && detectedTextElements.length === 0" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                  <div>
                    <p class="text-sm text-yellow-800 font-medium">No text detected in this image</p>
                    <p class="text-xs text-yellow-700">You can manually enter text in the fields below</p>
                  </div>
                </div>
              </div>

              <!-- Error Message -->
              <div v-else-if="textDetectionError" class="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div>
                    <p class="text-sm text-red-800 font-medium">Failed to detect text</p>
                    <p class="text-xs text-red-700">{{ textDetectionError }}</p>
                    <button
                      @click="detectText"
                      class="text-xs text-red-800 underline hover:no-underline mt-1"
                    >
                      Try again
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div
              v-for="(edit, index) in textEdits"
              :key="`edit-${index}`"
              class="bg-gray-50 rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <span class="text-sm font-medium text-gray-700">Edit {{ index + 1 }}</span>
                <button
                  v-if="textEdits.length > 1"
                  @click="removeTextEdit(index)"
                  class="text-red-500 hover:text-red-700 transition-colors"
                >
                  <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>

              <div class="space-y-3">
                <div>
                  <label class="block text-xs font-medium text-gray-600 mb-1">Text currently in the image:</label>
                  <input
                    v-model="edit.existingText"
                    type="text"
                    placeholder="Enter the exact text you see in the image"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-600 mb-1">New text:</label>
                  <input
                    v-model="edit.newText"
                    type="text"
                    placeholder="Text to replace it with"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>
            </div>

            <!-- Add Edit Button -->
            <button
              v-if="textEdits.length < 5"
              @click="addTextEdit"
              class="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-purple-300 hover:text-purple-600 transition-colors flex items-center justify-center space-x-2"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              <span>Add Another Edit</span>
            </button>

            <div v-if="textEdits.length >= 5" class="text-xs text-gray-500 text-center">
              Maximum of 5 text edits allowed
            </div>
          </div>
        </div>

        <!-- Remove Text Mode -->
        <div v-else-if="editMode === 'remove'">
          <!-- Collapsible Detect Text Section -->
          <div class="mb-4">
            <button
              @click="toggleDetectedTextSection"
              class="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
            >
              <div class="flex items-center space-x-2">
                <span>AI Detected Text in Image</span>
                <div v-if="isDetectingText" class="flex items-center space-x-1 text-blue-600">
                  <svg class="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-xs">Detecting text...</span>
                </div>
                <div v-else-if="detectedTextElements.length > 0" class="inline-flex items-center px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
                  {{ detectedTextElements.length }} found
                </div>
                <div v-else-if="hasDetectedText" class="inline-flex items-center px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                  None found
                </div>
              </div>
              <svg
                :class="['h-4 w-4 transition-transform duration-200', showDetectedTextSection ? 'rotate-180' : '']"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Collapsible Content -->
            <div
              v-show="showDetectedTextSection"
              class="mt-3 transition-all duration-300 ease-in-out overflow-hidden"
            >
              <!-- Detecting State -->
              <div v-if="isDetectingText" class="p-4 bg-blue-50 rounded-lg border border-blue-200 text-center">
                <div class="flex items-center justify-center space-x-2 text-blue-700">
                  <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="font-medium">Analyzing image for text...</span>
                </div>
                <p class="text-sm text-blue-600 mt-1">This usually takes a few seconds</p>
              </div>

              <!-- Detected Text Options -->
              <div v-else-if="detectedTextElements.length > 0">
                <p class="text-xs text-gray-600 mb-2">Click any text to add it to the removal list:</p>
                <div class="flex flex-wrap gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <button
                    v-for="(element, index) in detectedTextElements"
                    :key="`detected-remove-${index}`"
                    @click="selectTextToRemove(element.text)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium bg-white border border-blue-300 text-blue-800 rounded-full hover:bg-blue-100 transition-colors duration-150"
                    :title="`${element.description} (${element.confidence} confidence)`"
                  >
                    "{{ element.text }}"
                  </button>
                </div>
              </div>

              <!-- No Text Found Message -->
              <div v-else-if="hasDetectedText && detectedTextElements.length === 0" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                  <div>
                    <p class="text-sm text-yellow-800 font-medium">No text detected in this image</p>
                    <p class="text-xs text-yellow-700">You can manually enter text to remove below</p>
                  </div>
                </div>
              </div>

              <!-- Error Message -->
              <div v-else-if="textDetectionError" class="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div>
                    <p class="text-sm text-red-800 font-medium">Failed to detect text</p>
                    <p class="text-xs text-red-700">{{ textDetectionError }}</p>
                    <button
                      @click="detectText"
                      class="text-xs text-red-800 underline hover:no-underline mt-1"
                    >
                      Try again
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Text to remove from the image:
            </label>

            <div
              v-for="(text, index) in textsToRemove"
              :key="`remove-${index}`"
              class="bg-gray-50 rounded-lg p-4"
            >
              <div class="flex items-center justify-between">
                <input
                  v-model="textsToRemove[index]"
                  type="text"
                  placeholder="Enter text to remove from the image"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 mr-2"
                />
                <button
                  v-if="textsToRemove.length > 1"
                  @click="removeTextItem(index)"
                  class="text-red-500 hover:text-red-700 transition-colors"
                >
                  <svg class="w-3 h-3 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Add Text Button -->
            <button
              v-if="textsToRemove.length < 5"
              @click="addTextToRemove"
              class="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-purple-300 hover:text-purple-600 transition-colors flex items-center justify-center space-x-2"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              <span>Add Another Text</span>
            </button>

            <div v-if="textsToRemove.length >= 5" class="text-xs text-gray-500 text-center">
              Maximum of 5 text removals allowed
            </div>
          </div>
        </div>
          </div>

          <!-- Brief Content Section -->
          <div v-if="briefContent && briefContent.trim()" class="border-t border-gray-200 bg-white">
            <div class="p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Campaign Brief
              </h4>
              <div 
                class="text-sm text-gray-700 leading-relaxed overflow-y-auto prose prose-sm prose-gray"
                style="max-height: calc(100vh - 300px);"
                v-html="renderedBrief"
              ></div>
            </div>
          </div>
        </div>

        <!-- Right Panel -->
        <div class="flex-1 bg-white overflow-hidden flex flex-col max-w-2xl">
          <!-- Right Panel Header (only for non-Add Text modes) -->
          <div v-if="editMode !== 'addText'" class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Image Preview</h3>
            <p class="text-sm text-gray-500">The selected image will be processed according to your settings</p>
          </div>

          <!-- Right Panel Content -->
          <div class="flex-1 overflow-hidden">
            <!-- Add Text Mode: FilerobotImageEditor -->
            <div v-if="editMode === 'addText'" class="h-full relative">
              <div v-if="!isImageEditorReady" class="absolute inset-0 flex items-center justify-center bg-white z-10">
                <div class="text-center">
                  <svg class="w-8 h-8 mx-auto text-purple-600 animate-spin" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p class="mt-3 text-sm text-gray-500">Loading image editor...</p>
                </div>
              </div>

              <!-- FilerobotImageEditor Container -->
              <div
                id="editor_container"
                class="w-full h-full"
                style="background: #f8f9fa; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"
              ></div>
            </div>

            <!-- Other Modes: Image Preview -->
            <div v-else class="h-full flex items-center justify-center p-8">
              <div class="max-w-2xl w-full">
                <div class="bg-gray-50 rounded-lg p-6 text-center">
                  <img
                    :src="imageUrl"
                    :alt="imageAlt"
                    class="max-w-full h-auto rounded border border-gray-200 mx-auto"
                    style="max-height: 500px;"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="px-6 py-4 bg-gray-50 rounded-b-xl flex justify-end space-x-3">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="handleSubmit"
          :disabled="!canSubmit"
          class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {{ editMode === 'variation' ? 'Create Variation' : editMode === 'switch' ? 'Switch Image' : editMode === 'remove' ? 'Remove Text' : editMode === 'addText' ? 'Apply Image Changes' : 'Apply Text Changes' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, nextTick } from '@vue/runtime-core';
import { marked } from 'marked';
import * as Utils from '../../../client-old/utils/Utils';
import * as OrganizationSettings from '../../services/organization-settings.js';
// FilerobotImageEditor loaded from CDN - available as window.FilerobotImageEditor

interface TextEdit {
  existingText: string;
  newText: string;
}

interface DetectedTextElement {
  text: string;
  description: string;
  confidence: string;
}

export default defineComponent({
  name: 'ImageEditModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    imageUrl: {
      type: String,
      required: true
    },
    imageAlt: {
      type: String,
      default: ''
    },
    briefContent: {
      type: String,
      default: ''
    },
  },
  emits: ['close', 'submit'],
  setup(props, { emit }) {
    const editMode = ref<'variation' | 'text' | 'switch' | 'remove' | 'addText'>('variation');
    const variationDescription = ref('');
    const switchImageDescription = ref('');
    const textEdits = ref<TextEdit[]>([
      { existingText: '', newText: '' }
    ]);
    const textsToRemove = ref<string[]>(['']);

    // Text detection state
    const isDetectingText = ref(false);
    const detectedTextElements = ref<DetectedTextElement[]>([]);
    const hasDetectedText = ref(false);
    const textDetectionError = ref('');
    const showDetectedTextSection = ref(false);

    // Cache for detected text to avoid redundant API calls
    const textDetectionCache = ref<Map<string, {
      elements: DetectedTextElement[];
      error: string;
      hasDetected: boolean;
    }>>(new Map());

    // FilerobotImageEditor state
    const isImageEditorReady = ref(false);
    const imageEditor = ref<any>(null);

    // Brief text selection state
    const selectedText = ref('');
    const briefContentRef = ref<HTMLElement | null>(null);

    // Rendered markdown for brief content
    const renderedBrief = computed(() => {
      if (!props.briefContent) return '';
      marked.setOptions({ breaks: true, gfm: true });
      return marked(props.briefContent);
    });

    // Reset form when modal opens/closes
    watch(() => props.show, (newShow) => {
      if (newShow) {
        // Reset to default state when opening
        editMode.value = 'variation';
        variationDescription.value = '';
        switchImageDescription.value = '';
        textEdits.value = [{ existingText: '', newText: '' }];
        textsToRemove.value = [''];
        // Reset text detection state
        detectedTextElements.value = [];
        hasDetectedText.value = false;
        textDetectionError.value = '';
        isDetectingText.value = false;
        showDetectedTextSection.value = false;
        // Clear the cache when modal closes
        textDetectionCache.value.clear();
        // Reset FilerobotImageEditor state
        isImageEditorReady.value = false;

        // Reset text selection state
        selectedText.value = '';
        // Clean up any existing editor
        if (imageEditor.value) {
          try {
            imageEditor.value.terminate();
          } catch (e) {
            // Ignore cleanup errors
          }
          imageEditor.value = null;
        }
      }
    });

    // Auto-detect text when switching to text or remove mode
    watch(() => editMode.value, (newMode) => {
      console.log('🟠 EditMode changed to:', newMode);

      if ((newMode === 'text' || newMode === 'remove') && props.show) {
        // Auto-start text detection
        showDetectedTextSection.value = true;
        detectText();
      } else if (newMode === 'addText' && props.show) {
        console.log('Switched to Add Text mode - initializing FilerobotImageEditor');
        // Initialize FilerobotImageEditor when switching to Add Text mode
        nextTick(() => {
          initializeImageEditor();
        });
      }
    });

    const canSubmit = computed(() => {
      if (editMode.value === 'variation') {
        return variationDescription.value.trim().length > 0;
      } else if (editMode.value === 'switch') {
        return switchImageDescription.value.trim().length > 0;
      } else if (editMode.value === 'remove') {
        // At least one text to remove must be filled
        return textsToRemove.value.some(text => text.trim().length > 0);
      } else if (editMode.value === 'addText') {
        // For Add Text mode, require that image editor is ready
        return isImageEditorReady.value;
      } else {
        // At least one text edit must have both fields filled
        return textEdits.value.some(edit =>
          edit.existingText.trim().length > 0 && edit.newText.trim().length > 0
        );
      }
    });


    const addTextEdit = () => {
      if (textEdits.value.length < 5) {
        textEdits.value.push({ existingText: '', newText: '' });
      }
    };

    const removeTextEdit = (index: number) => {
      if (textEdits.value.length > 1) {
        textEdits.value.splice(index, 1);
      }
    };

    const addTextToRemove = () => {
      if (textsToRemove.value.length < 5) {
        textsToRemove.value.push('');
      }
    };

    const removeTextItem = (index: number) => {
      if (textsToRemove.value.length > 1) {
        textsToRemove.value.splice(index, 1);
      }
    };

    // Font loading utility functions
    const loadFontOnDemand = async (fontFamily: string): Promise<void> => {
      // Skip if fontFamily is empty or undefined
      if (!fontFamily || fontFamily.trim() === '') {
        console.warn('Empty font family provided to loadFontOnDemand');
        return Promise.resolve();
      }

      // Skip if it's a system font
      const systemFonts = ['Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Verdana', 'Tahoma', 'Trebuchet MS', 'Impact', 'Comic Sans MS', 'Courier New', 'Lucida Console'];
      if (systemFonts.includes(fontFamily)) {
        return Promise.resolve();
      }

      // Check if font is already loaded
      if (document.querySelector(`link[data-font="${fontFamily}"]`)) {
        return Promise.resolve();
      }

      try {
        // Try loading as custom font from organization settings first
        return await loadCustomFont(fontFamily);
      } catch (error) {
        // If custom font fails, try loading as Google Font directly (no CORS check)
        try {
          return await loadGoogleFont(fontFamily);
        } catch (googleError) {
          console.warn(`Failed to load font: ${fontFamily}`, error, googleError);
          throw error;
        }
      }
    };

    // Load Google Font directly (no CORS check)
    const loadGoogleFont = (fontFamily: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(/\s+/g, '+')}:wght@300;400;500;600;700&display=swap`;
        link.rel = 'stylesheet';
        link.setAttribute('data-font', fontFamily); // Track loaded fonts

        link.onload = () => {
          // Wait a bit for font to be fully available
          setTimeout(resolve, 100);
        };

        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    // Load custom font from organization settings using cssUrl
    const loadCustomFont = async (fontFamily: string): Promise<void> => {
      try {
        // Get custom font data from organization settings
        const customFontsString = await OrganizationSettings.getOrganizationSetting('customFonts');
        if (customFontsString) {
          const customFonts = JSON.parse(customFontsString);
          const customFont = customFonts.find((font: any) =>
            (font.name === fontFamily) || (font.family === fontFamily) || (font.cssValue && font.cssValue.includes(fontFamily))
          );

          if (customFont && customFont.cssUrl) {
            return new Promise((resolve, reject) => {
              const link = document.createElement('link');
              link.href = customFont.cssUrl; // Use cssUrl from your data
              link.rel = 'stylesheet';
              link.setAttribute('data-font', fontFamily);

              link.onload = () => setTimeout(resolve, 100);
              link.onerror = reject;
              document.head.appendChild(link);
            });
          }
        }

        // If no custom font URL found, reject
        throw new Error(`Custom font ${fontFamily} not found in organization settings`);
      } catch (error) {
        console.error('Error loading custom font:', error);
        throw error;
      }
    };

    // Load custom fonts from organization settings
    const loadCustomFonts = async () => {
      try {
        const customFontsString = await OrganizationSettings.getOrganizationSetting('customFonts');
        if (customFontsString) {
          const customFonts = JSON.parse(customFontsString);
          return customFonts.map((font: any) => ({
            label: font.name,
            value: font.name // Use font name for dynamic loading lookup
          }));
        }
      } catch (error) {
        console.error('Error loading custom fonts:', error);
      }
      return [];
    };

    // Initialize FilerobotImageEditor
    const initializeImageEditor = async () => {
      console.log('Initializing FilerobotImageEditor...');

      try {
        const container = document.getElementById('editor_container');
        if (!container) {
          console.error('FilerobotImageEditor container not found');
          return;
        }

        // Clean up any existing editor
        if (imageEditor.value) {
          try {
            imageEditor.value.terminate();
          } catch (e) {
            // Ignore cleanup errors
          }
        }

        // Access FilerobotImageEditor from window (loaded via CDN)
        const FilerobotImageEditor = (window as any).FilerobotImageEditor;
        if (!FilerobotImageEditor) {
          console.error('FilerobotImageEditor not loaded from CDN');
          return;
        }

        // Load custom fonts
        const customFonts = await loadCustomFonts();
        console.log('Loaded custom fonts:', customFonts);

        // Destructure TABS and TOOLS from FilerobotImageEditor as per documentation
        const { TABS, TOOLS } = FilerobotImageEditor;

        // Prepare font list with custom fonts - proper label/value structure
        const standardFonts = [
          // Standard system fonts
          { label: 'Arial', value: 'Arial' },
          { label: 'Helvetica', value: 'Helvetica' },
          { label: 'Times New Roman', value: 'Times New Roman' },
          { label: 'Georgia', value: 'Georgia' },
          { label: 'Verdana', value: 'Verdana' },
          { label: 'Tahoma', value: 'Tahoma' },
          { label: 'Trebuchet MS', value: 'Trebuchet MS' },
          { label: 'Impact', value: 'Impact' },
          { label: 'Comic Sans MS', value: 'Comic Sans MS' },
          { label: 'Courier New', value: 'Courier New' },
          { label: 'Lucida Console', value: 'Lucida Console' },
          // Popular Google Fonts that will be loaded dynamically
          { label: 'Open Sans', value: 'Open Sans' },
          { label: 'Roboto', value: 'Roboto' },
          { label: 'Lato', value: 'Lato' },
          { label: 'Montserrat', value: 'Montserrat' },
          { label: 'Source Sans Pro', value: 'Source Sans Pro' },
          { label: 'Oswald', value: 'Oswald' },
          { label: 'Raleway', value: 'Raleway' },
          { label: 'PT Sans', value: 'PT Sans' },
          { label: 'Ubuntu', value: 'Ubuntu' },
          { label: 'Nunito', value: 'Nunito' },
        ];

        // Combine all fonts - custom fonts first, then separator, then standard fonts
        const allFonts = customFonts.length > 0
          ? [
              ...customFonts,
              ...standardFonts
            ]
          : standardFonts;

        // Initialize the image editor with high-resolution config
        const config = {
          source: props.imageUrl,
          removeSaveButton: true, // Hide the internal Save button
          onSave: null, // Remove save callback since we're handling save externally
          // Ultra high resolution settings
          savingPixelRatio: 8, // Save at x resolution for maximum sharpness
          previewPixelRatio: 4, // Preview at x for ultra-crisp display
          observePluginContainerSize: true, // Responsive sizing
          useCloudimage: false, // Disable cloudimage compression
          disableZooming: false, // Allow zooming for detail work
          // Enhanced zoom and pan settings
          showCanvasOnly: false, // Show full interface with zoom controls
          useZoomPresetsMenu: true, // Enable zoom presets dropdown
          crossOrigin: 'anonymous', // Allow cross-origin image manipulation
          // Layout and sizing settings
          containerElementsSpacing: 8, // Better spacing between elements
          showImageSize: false, // Hide image size indicator to save space
          // Maximum image quality settings
          quality: 0.9, // High quality JPEG (0.0-1.0)
          format: 'jpg', // JPG format as requested
          // Canvas settings for ultra-high rendering
          canvas: {
            width: 'auto',
            height: 'auto',
            preserveImageSize: true,
            maxCanvasSize: 8192 // Support very large canvases
          },
          // Enable all zoom and pan functionality
          zoomConfig: {
            wheel: true, // Mouse wheel zoom
            doubleClick: true, // Double-click to zoom
            pinch: true, // Touch pinch zoom
            pan: true, // Enable panning when zoomed
            rangeSlider: true // Show zoom slider
          },
          // Advanced rendering settings
          imageRendering: 'pixelated', // Preserve sharp edges
          smoothing: false, // Disable smoothing for pixel-perfect rendering
          annotationsCommon: {
            fill: '#6B46C1' // Purple color to match theme
          },
          excludedTools: ['Image', 'Ellipse', 'Polygon', 'Pen', 'Line', 'Arrow'],
          Text: {
            text: selectedText.value || 'Text', // Use selected text if available
            fontSize: 64, // Much larger font for ultra high-res
            strokeWidth: 0, // No stroke initially
            fontFamily: 'Arial',
            letterSpacing: 0.5, // Better text spacing at high res
            fonts: allFonts,
            onFontChange: async (fontFamily: string, reRenderCanvasFn: () => void) => {
              try {
                console.log(`Loading font on demand: ${fontFamily}`);
                await loadFontOnDemand(fontFamily);
                console.log(`Font loaded successfully: ${fontFamily}`);
                reRenderCanvasFn();
              } catch (error) {
                console.warn(`Failed to load font: ${fontFamily}`, error);
                reRenderCanvasFn(); // Still re-render with fallback
              }
            }
          },
          tabsIds: [TABS.ANNOTATE], // Only show Annotate tab, removed TABS.ADJUST and TABS.WATERMARK
          defaultTabId: TABS.ANNOTATE,
          defaultToolId: TOOLS.TEXT
        };

        // Create FilerobotImageEditor instance
        imageEditor.value = new FilerobotImageEditor(container, config);

        // Render with onClose callback
        imageEditor.value.render({
          onClose: (closingReason: any) => {
            console.log('Image editor closed:', closingReason);
            imageEditor.value.terminate();
          }
        });

		setTimeout(() => {
			console.log('=== EDITOR DEBUG INFO ===');
			console.log('Editor instance:', imageEditor.value);
			console.log('Editor type:', typeof imageEditor.value);

			// Check the internal refs
			console.log('Has getCurrentImgDataFnRef:', !!imageEditor.value.getCurrentImgDataFnRef);
			console.log('getCurrentImgDataFnRef:', imageEditor.value.getCurrentImgDataFnRef);
			console.log('getCurrentImgDataFnRef.current:', imageEditor.value.getCurrentImgDataFnRef?.current);

			// Test the method
			const testResult = imageEditor.value.getCurrentImgData({
			name: 'test',
			extension: 'jpg',
			quality: 0.9
			});
			console.log('Test getCurrentImgData result:', testResult);
			console.log('Test result keys:', Object.keys(testResult));
			console.log('Has imageBase64:', !!testResult.imageBase64);

			// Check container content
			console.log('Container has content:', imageEditor.value.container?.innerHTML?.length > 100);
			console.log('Container innerHTML length:', imageEditor.value.container?.innerHTML?.length);
		}, 2000); // Wait 2 seconds for everything to initialize

        isImageEditorReady.value = true;
        console.log('FilerobotImageEditor initialized successfully');
      } catch (error) {
        console.error('Error initializing FilerobotImageEditor:', error);
      }
    };


    const handleSubmit = async () => {
      if (!canSubmit.value) return;

      let submitData: any = {
        imageUrl: props.imageUrl,
        editMode: editMode.value,
        variationDescription: editMode.value === 'variation' ? variationDescription.value.trim() : null,
        switchImageDescription: editMode.value === 'switch' ? switchImageDescription.value.trim() : null,
        textEdits: editMode.value === 'text' ? textEdits.value.filter(edit =>
          edit.existingText.trim().length > 0 && edit.newText.trim().length > 0
        ) : null,
        textsToRemove: editMode.value === 'remove' ? textsToRemove.value.filter(text =>
          text.trim().length > 0
        ) : null
      };

      // Handle Add Text mode - save the edited image
      if (editMode.value === 'addText') {
        try {
          if (!imageEditor.value) {
            throw new Error('Image editor not initialized');
          }

          // Get the processed image data directly from the editor
          const result = imageEditor.value.getCurrentImgData({
            name: 'edited-image',
            extension: 'jpg',
            quality: 0.9
          });

          console.log('Full result:', result);

          // CORRECTED: Access imageData from the result object
          const imageData = result.imageData;

          if (imageData && imageData.imageBase64) {
            submitData.editedImageData = {
              imageBase64: imageData.imageBase64,
              fullName: imageData.fullName,
              width: imageData.width,
              height: imageData.height
            };
            console.log('Successfully saved edited image from FilerobotImageEditor');
          } else {
            throw new Error('No image data received from editor');
          }
        } catch (error) {
          console.error('Error saving image from editor:', error);
          return; // Don't submit if there's an error
        }
      }

      emit('submit', submitData);
      emit('close');
    };

    // Detect text in image using AI with caching
    const detectText = async () => {
      if (isDetectingText.value || !props.imageUrl) return;

      // Check cache first
      const cacheKey = props.imageUrl;
      const cachedResult = textDetectionCache.value.get(cacheKey);
      
      if (cachedResult) {
        console.log('Using cached text detection result for:', cacheKey);
        detectedTextElements.value = cachedResult.elements;
        textDetectionError.value = cachedResult.error;
        hasDetectedText.value = cachedResult.hasDetected;
        return;
      }

      console.log('Making new text detection API call for:', cacheKey);
      isDetectingText.value = true;
      textDetectionError.value = '';

      try {
        const response = await fetch(`${Utils.URL_DOMAIN}/images/detect-text`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            imageUrl: props.imageUrl
          })
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const result = await response.json();
        const elements = result.textElements || [];
        
        // Update state
        detectedTextElements.value = elements;
        hasDetectedText.value = true;
        textDetectionError.value = '';

        // Cache the successful result
        textDetectionCache.value.set(cacheKey, {
          elements: elements,
          error: '',
          hasDetected: true
        });

      } catch (error) {
        console.error('Error detecting text:', error);
        const errorMessage = 'Failed to detect text. Please try again or enter text manually.';
        
        // Update state
        textDetectionError.value = errorMessage;
        detectedTextElements.value = [];
        hasDetectedText.value = true;

        // Cache the error result to avoid repeated failed requests
        textDetectionCache.value.set(cacheKey, {
          elements: [],
          error: errorMessage,
          hasDetected: true
        });
      } finally {
        isDetectingText.value = false;
      }
    };

    // Select detected text and populate the first empty field or create new edit
    const selectDetectedText = (text: string) => {
      // Find the first text edit that doesn't have existing text filled
      const emptyEditIndex = textEdits.value.findIndex(edit => edit.existingText.trim() === '');

      if (emptyEditIndex !== -1) {
        // Fill the empty field
        textEdits.value[emptyEditIndex].existingText = text;
      } else {
        // All fields are filled, add a new edit if under the limit
        if (textEdits.value.length < 5) {
          textEdits.value.push({ existingText: text, newText: '' });
        } else {
          // Replace the last edit
          textEdits.value[textEdits.value.length - 1].existingText = text;
        }
      }
    };

    // Select detected text for removal
    const selectTextToRemove = (text: string) => {
      // Find the first empty text removal field
      const emptyRemoveIndex = textsToRemove.value.findIndex(t => t.trim() === '');

      if (emptyRemoveIndex !== -1) {
        // Fill the empty field
        textsToRemove.value[emptyRemoveIndex] = text;
      } else {
        // All fields are filled, add a new field if under the limit
        if (textsToRemove.value.length < 5) {
          textsToRemove.value.push(text);
        } else {
          // Replace the last field
          textsToRemove.value[textsToRemove.value.length - 1] = text;
        }
      }
    };

    // Toggle the collapsed section for detected text
    const toggleDetectedTextSection = () => {
      showDetectedTextSection.value = !showDetectedTextSection.value;
    };

    // Handle text selection from brief
    const handleTextSelection = () => {
      const selection = window.getSelection();
      if (selection && selection.toString().trim()) {
        selectedText.value = selection.toString().trim();
        console.log('Selected text from brief:', selectedText.value);

        // If the image editor is already initialized, we need to reinitialize it
        // with the new default text since FilerobotImageEditor doesn't allow
        // dynamic updates to text defaults
        if (isImageEditorReady.value && imageEditor.value) {
          console.log('Reinitializing image editor with new default text');
          // Clean up the current editor
          try {
            imageEditor.value.terminate();
          } catch (e) {
            console.log('Error terminating editor:', e);
          }
          imageEditor.value = null;
          isImageEditorReady.value = false;

          // Reinitialize with new text
          nextTick(() => {
            initializeImageEditor();
          });
        }
      }
    };

    // Watch for selected text changes and reinitialize editor if needed
    watch(selectedText, (newText) => {
      if (editMode.value === 'addText' && isImageEditorReady.value && imageEditor.value) {
        console.log('Selected text changed, reinitializing editor with:', newText);
        // Clean up the current editor
        try {
          imageEditor.value.terminate();
        } catch (e) {
          console.log('Error terminating editor:', e);
        }
        imageEditor.value = null;
        isImageEditorReady.value = false;

        // Reinitialize with new text
        nextTick(() => {
          initializeImageEditor();
        });
      }
    });

    return {
      editMode,
      variationDescription,
      switchImageDescription,
      textEdits,
      textsToRemove,
      canSubmit,
      addTextEdit,
      removeTextEdit,
      addTextToRemove,
      removeTextItem,
      handleSubmit,
      // Text detection
      isDetectingText,
      detectedTextElements,
      hasDetectedText,
      textDetectionError,
      showDetectedTextSection,
      detectText,
      selectDetectedText,
      selectTextToRemove,
      toggleDetectedTextSection,
      // FilerobotImageEditor
      isImageEditorReady,
      imageEditor,
      initializeImageEditor,
      // Brief text selection
      selectedText,
      briefContentRef,
      handleTextSelection,
      // Brief content rendering
      renderedBrief
    };
  }
});
</script>
