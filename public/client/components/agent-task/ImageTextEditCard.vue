<template>
  <div class="rounded-lg overflow-hidden bg-white border border-gray-200 shadow-sm inline-block max-w-[300px]">
    <!-- Header -->
    <div class="p-2 bg-purple-50 border-b border-gray-200 flex justify-between items-center">
      <h4 class="font-medium text-sm text-gray-700 truncate">Image with Text Overlay</h4>
      <button
        @click="openEditor"
        class="text-xs bg-purple-100 hover:bg-purple-200 text-purple-700 px-2 py-1 rounded transition-colors"
      >
        Edit
      </button>
    </div>

    <div class="relative">
      <!-- Loading placeholder -->
      <div v-if="!imageLoaded" class="w-[300px] h-[200px] bg-gray-100 flex items-center justify-center">
        <div class="flex flex-col items-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2"></div>
          <span class="text-sm text-gray-500">Loading image...</span>
        </div>
      </div>

      <!-- Hidden image preloader -->
      <img
        :src="imageUrl"
        alt=""
        class="hidden"
        @load="handleImageLoad"
        @error="handleImageError" />

      <!-- Actual image with text overlay (only shown after loading) -->
      <div v-if="imageLoaded" class="w-[300px] h-[200px] flex items-center justify-center overflow-hidden bg-gray-50 relative">
        <img
          :src="imageUrl"
          alt="Image with text overlay"
          class="max-w-full max-h-full object-contain"
          @click="openImageInNewTab(imageUrl)"
          style="cursor: pointer;" />

        <!-- Text overlay -->
        <div
          class="absolute text-overlay"
          :style="{
            left: x + 'px',
            top: y + 'px',
            fontFamily: font,
            fontSize: fontSize + 'px',
            color: color
          }"
        >
          {{ text }}
        </div>
      </div>
    </div>

    <div class="p-2 bg-white border-t border-gray-100 text-center">
      <div class="flex justify-between items-center">
        <span class="text-xs text-gray-500 mr-2 flex-shrink-1">Click to view full size</span>
        <a :href="imageUrl" target="_blank" class="text-purple-600 hover:text-purple-800 text-xs whitespace-nowrap flex-shrink-0 min-w-[80px] text-right">
          Open in new tab
        </a>
      </div>
    </div>

    <!-- Editor Modal -->
    <div v-if="showEditor" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-semibold">Edit Text Overlay</h3>
          <button @click="closeEditor" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="p-4">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Text</label>
            <input
              v-model="editedText"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Enter text to overlay on image"
            />
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Position</label>
            <div class="grid grid-cols-2 gap-2">
              <div>
                <label class="block text-xs text-gray-500 mb-1">X Coordinate</label>
                <input
                  v-model.number="editedX"
                  type="number"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="X position"
                />
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">Y Coordinate</label>
                <input
                  v-model.number="editedY"
                  type="number"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Y position"
                />
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Font</label>
            <select
              v-model="editedFont"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="Arial">Arial</option>
              <option value="Verdana">Verdana</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Georgia">Georgia</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
            <input
              v-model.number="editedFontSize"
              type="number"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Font size"
            />
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Text Color</label>
            <input
              v-model="editedColor"
              type="color"
              class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
        </div>

        <div class="p-4 border-t border-gray-200 flex justify-end space-x-2">
          <button
            @click="closeEditor"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="saveChanges"
            class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from '@vue/runtime-core';
import ImageRenderer from './ImageRenderer.vue';

interface ImageTextEditCardProps {
  imageUrl: string;
  text: string;
  x: number;
  y: number;
  font: string;
  fontSize: number;
  color: string;
  imageId: string;
}

export default defineComponent({
  name: 'ImageTextEditCard',
  extends: ImageRenderer,
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    text: {
      type: String,
      default: ''
    },
    x: {
      type: Number,
      default: 0
    },
    y: {
      type: Number,
      default: 0
    },
    font: {
      type: String,
      default: 'Arial'
    },
    fontSize: {
      type: Number,
      default: 16
    },
    color: {
      type: String,
      default: '#000000'
    },
    imageId: {
      type: String,
      required: true
    }
  },
  emits: ['update:text-overlay', 'image-loaded', 'image-error'],
  setup(props: ImageTextEditCardProps, { emit }) {
    const imageLoaded = ref(false);
    const showEditor = ref(false);

    // Editor state
    const editedText = ref(props.text);
    const editedX = ref(props.x);
    const editedY = ref(props.y);
    const editedFont = ref(props.font);
    const editedFontSize = ref(props.fontSize);
    const editedColor = ref(props.color);

    // Computed property to ensure the image URL is properly formatted
    const getFullImageUrl = () => {
      const url = props.imageUrl || '';

      // If it's already an absolute URL (starts with http:// or https://)
      if (url.match(/^https?:\/\//)) {
        return url;
      }

      // Check if it's a CloudFront URL that might be missing the protocol
      if (url.includes('cloudfront.net')) {
        return 'https://' + url.replace(/^[^a-zA-Z0-9]+/, '');
      }

      // If it's a relative URL that doesn't start with a slash, add one
      if (url && !url.startsWith('/')) {
        return '/' + url;
      }

      return url;
    };

    // Override base methods to update local state
    const handleImageLoad = () => {
      imageLoaded.value = true;
      emit('image-loaded', props.imageId);
    };

    const handleImageError = (event: Event) => {
      console.error(`Image load error for URL: ${props.imageUrl}`);
      emit('image-error', event, props.imageId);
    };

    // Editor methods
    const openEditor = () => {
      // Reset editor values to current props
      editedText.value = props.text;
      editedX.value = props.x;
      editedY.value = props.y;
      editedFont.value = props.font;
      editedFontSize.value = props.fontSize;
      editedColor.value = props.color;
      showEditor.value = true;
    };

    const closeEditor = () => {
      showEditor.value = false;
    };

    const saveChanges = () => {
      // Emit the updated text overlay data
      emit('update:text-overlay', {
        text: editedText.value,
        x: editedX.value,
        y: editedY.value,
        font: editedFont.value,
        fontSize: editedFontSize.value,
        color: editedColor.value
      });
      showEditor.value = false;
    };

    return {
      imageLoaded,
      imageUrl: getFullImageUrl(),
      handleImageLoad,
      handleImageError,
      showEditor,
      editedText,
      editedX,
      editedY,
      editedFont,
      editedFontSize,
      editedColor,
      openEditor,
      closeEditor,
      saveChanges,
      x: props.x,
      y: props.y,
      font: props.font,
      fontSize: props.fontSize,
      color: props.color
    };
  }
});
</script>

<style scoped>
.text-overlay {
  position: absolute;
  pointer-events: none;
  user-select: none;
}
</style>
