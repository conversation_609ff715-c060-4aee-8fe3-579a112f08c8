<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">Image Text Edit Demo</h1>

    <!-- Sample image text edit card -->
    <div class="mb-4">
      <h2 class="text-xl font-semibold mb-2">Sample Image with Text Overlay</h2>
      <ImageTextEditCard
        :image-id="'demo-image-1'"
        :image-url="'https://picsum.photos/300/200'"
        :text="'Hello World'"
        :x="50"
        :y="100"
        :font="'Arial'"
        :font-size="24"
        :color="'#FF0000'"
        @image-loaded="handleImageLoad"
        @image-error="handleImageError"
        @update:text-overlay="handleTextOverlayUpdate"
      />
    </div>

    <!-- JSON output -->
    <div class="mt-8">
      <h2 class="text-xl font-semibold mb-2">Current Text Overlay Data</h2>
      <pre class="bg-gray-100 p-4 rounded">{{ currentTextOverlayData }}</pre>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from '@vue/runtime-core';
import ImageTextEditCard from './ImageTextEditCard.vue';

export default defineComponent({
  name: 'ImageTextEditDemo',
  components: {
    ImageTextEditCard
  },
  setup() {
    const currentTextOverlayData = ref({
      text: 'Hello World',
      x: 50,
      y: 100,
      font: 'Arial',
      fontSize: 24,
      color: '#FF0000'
    });

    const handleImageLoad = (imageId: string) => {
      console.log('Image loaded:', imageId);
    };

    const handleImageError = (event: Event, imageId: string) => {
      console.error('Image error:', event, imageId);
    };

    const handleTextOverlayUpdate = (updateData: any) => {
      console.log('Text overlay updated:', updateData);
      currentTextOverlayData.value = updateData;
    };

    return {
      currentTextOverlayData,
      handleImageLoad,
      handleImageError,
      handleTextOverlayUpdate
    };
  }
});
</script>
