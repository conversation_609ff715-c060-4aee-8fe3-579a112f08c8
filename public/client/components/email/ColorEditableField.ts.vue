<template>
  <div class="flex items-start">
    <div class="flex items-center h-5">
      <input
        :id="`field-${path}`"
        type="checkbox"
        v-model="editableFields[path].editable"
        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
      >
    </div>
    <div class="ml-2 w-full">
      <label :for="`field-${path}`" class="text-sm font-medium text-gray-700">
        {{ getFieldLabel(path) }}
      </label>
      <div class="mt-1">
        <input
          type="text"
          v-model="editableFields[path].description"
          :disabled="!editableFields[path].editable"
          :placeholder="getDefaultColorPlaceholder(path)"
          class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ColorEditableField',
  props: {
    path: {
      type: String,
      required: true
    },
    editableFields: {
      type: Object,
      required: true
    },
    componentTypesMap: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getFieldLabel(path) {
      let componentType = 'unknown';
      if (this.componentTypesMap) {
        for (const [contentId, type] of Object.entries(this.componentTypesMap)) {
          if (path.includes(contentId)) {
            componentType = type;
            break;
          }
        }
      }

      if (path.endsWith('.values.text')) {
        if (componentType === 'heading') return 'Heading Text';
        else if (componentType === 'text') return 'Text Content';
        else if (componentType === 'button') return 'Button Text';
        else return 'Text';
      } else if(path.endsWith('.values.action.values.href')) {
        return 'Image Link URL';
      } else if (path.endsWith('.values.src.url')) {
        return 'Image URL';
      } else if (path.endsWith('.values.altText')) {
        return 'Alt Text';
      } else if (path.endsWith('.values.href.values.href')) {
        return 'Button URL';
      } else if (path.endsWith('.color')) {
        return 'Color';
      } else if (path.endsWith('.backgroundColor')) {
        return 'Background Color';
      } else if (path.endsWith('.textColor')) {
        return 'Text Color';
      } else if (path.endsWith('.linkColor')) {
        return 'Link Color';
      } else if (path.endsWith('.columnsBackgroundColor')) {
        return 'Columns Background Color';
      } else {
        const lastPart = path.split('.').pop() || '';
        return lastPart.charAt(0).toUpperCase() + lastPart.slice(1);
      }
    },
    getDefaultColorPlaceholder(path) {
      // Provide descriptive placeholders for color fields
      if (path.endsWith('.color')) {
        return 'Describe what color to use for this text (e.g., "Use brand red #d72323")';
      } else if (path.endsWith('.backgroundColor')) {
        return 'Describe what background color to use (e.g., "Use brand black #000000")';
      } else if (path.endsWith('.textColor')) {
        return 'Describe what text color to use (e.g., "Use brand blue #1c2545")';
      } else if (path.endsWith('.linkColor')) {
        return 'Describe what link color to use (e.g., "Use brand blue #1155cc")';
      } else if (path.endsWith('.columnsBackgroundColor')) {
        return 'Describe what column background color to use (e.g., "Use white #ffffff")';
      }
      return 'Field description (optional)';
    }
  }
};
</script>
