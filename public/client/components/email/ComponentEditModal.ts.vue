<template>
  <!-- Full Screen Modal -->
  <div v-if="show" class="fixed inset-0 z-50 bg-white">
    <!-- Top Bar -->
    <div class="bg-white border-b px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Left section with title -->
        <div class="flex items-center gap-2">
          <h1 class="text-xl font-semibold">
            {{ componentMode === 'create' ? 'Create Custom Section' : 'Override Section' }}
          </h1>
        </div>

        <!-- Right section with buttons -->
        <div class="flex items-center gap-3">
          <!-- Save button -->
          <button
            @click="save"
            :disabled="!isValidComponent || isSaving"
            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center gap-2"
          >
            <svg v-if="isSaving" class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSaving ? 'Saving...' : 'Save Component' }}
          </button>

          <!-- Close button -->
          <button
            @click="close"
            class="p-2 rounded-full hover:bg-gray-100"
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex h-full" style="height: calc(100% - 72px);">
      <!-- Left Panel: Component Information -->
      <div class="w-96 border-r bg-gray-50 overflow-y-auto">
        <div class="p-6 space-y-6">
          <!-- Section Name -->
          <div class="space-y-2">
            <div class="flex justify-between items-center">
              <label class="block text-sm font-medium text-gray-700">Section Name</label>
              <div class="flex items-center" v-if="componentMode === 'override' || (componentMode === 'create' && component.orgId === orgId)">
                <span class="text-sm text-gray-700 mr-2">Active:</span>
                <ToggleItem
                  :state="component.active !== false"
                  @toggleChange="component.active = $event"
                  :showLabel="false"
                />
              </div>
            </div>
            <input
              type="text"
              v-model="component.name"
              placeholder="Enter section name..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
            <div v-if="!component.name.trim()" class="text-xs text-red-500">
              Section name is required
            </div>
          </div>

          <!-- Component Type Selection (for overriding only) -->
          <div v-if="componentMode === 'override' && !editingComponent" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Component to Override</label>
            <select
              v-model="component.id"
              @change="onComponentSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">Select a component</option>
              <option v-for="comp in globalComponents" :key="comp.id" :value="comp.id">
                {{ comp.name }}
              </option>
            </select>
          </div>

          <!-- AI Instructions Section -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">AI Instructions</label>
            <textarea
              v-model="component.description"
              rows="3"
              placeholder="Enter instructions for AI to use when editing this component..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            ></textarea>
            <p class="text-xs text-gray-500">These instructions will be used by AI to understand how to edit this component.</p>
          </div>

          <!-- JSON Section (readonly) -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Section JSON</label>
            <div class="relative">
              <textarea
                v-model="component.json"
                readonly
                rows="8"
                placeholder="JSON will be generated automatically by the editor on the right..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 font-mono text-xs resize-none cursor-not-allowed"
              ></textarea>
              <div class="absolute top-2 right-2">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0l-3-3m3 3l3-3m2-9a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div v-if="jsonError" class="text-red-500 text-xs">{{ jsonError }}</div>
            <div v-if="detectedComponentTypes && detectedComponentTypes.length > 0" class="text-green-600 text-xs">
              Detected component types: {{ detectedComponentTypes.join(', ') }}
            </div>
            <p class="text-xs text-gray-500">This field is automatically populated from the visual editor. Use the editor on the right to design your component.</p>
          </div>

          <!-- Editable AI Fields Section -->
          <div v-if="parsedComponentJson" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">AI-Editable Fields</label>
            <EditableFieldsSection
              :editable-fields="editableFields"
              :grouped-field-paths="groupedFieldPaths"
              :component-types-map="componentTypesMap"
              :component-names-map="componentNamesMap"
            />
          </div>

          <!-- Editor Status -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Editor Status</label>
            <div class="text-xs space-y-1">
              <div class="flex items-center space-x-2">
                <div :class="['w-2 h-2 rounded-full', isEditorReady ? 'bg-green-500' : 'bg-gray-300']"></div>
                <span>Editor {{ isEditorReady ? 'Ready' : 'Loading...' }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <div :class="['w-2 h-2 rounded-full', component.json ? 'bg-green-500' : 'bg-gray-300']"></div>
                <span>JSON {{ component.json ? 'Generated' : 'Empty' }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <div :class="['w-2 h-2 rounded-full', designLoaded ? 'bg-green-500' : 'bg-gray-300']"></div>
                <span>Design {{ designLoaded ? 'Loaded' : 'Not Loaded' }}</span>
              </div>
              <div v-if="editingComponent" class="flex items-center space-x-2">
                <div :class="['w-2 h-2 rounded-full', 'bg-blue-500']"></div>
                <span>Edit Mode</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Unlayer Editor -->
      <div class="flex-1 bg-white overflow-hidden flex flex-col">
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-900">Visual Email Component Editor</h3>
          <p class="text-sm text-gray-500">Design your email component using the visual editor below. Changes will automatically update the JSON.</p>
        </div>

        <!-- Loading state -->
        <div v-if="!isEditorReady" class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <svg class="w-8 h-8 mx-auto text-purple-600 animate-spin" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p class="mt-3 text-sm text-gray-500">Loading email editor...</p>
          </div>
        </div>

        <!-- Email Editor -->
        <div v-show="isEditorReady" class="flex-1">
          <EmailEditor
            v-if="fontSettingsLoaded"
            :project-id="'267562'"
            :tools="emailTools"
			:options="editorOptions"
            ref="emailEditor"
            @load="onEditorLoaded"
            style="height: 100%; width: 100%;"
          />
          <div v-else class="flex items-center justify-center h-full">
            <div class="text-center">
              <svg class="w-8 h-8 mx-auto text-purple-600 animate-spin" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p class="mt-3 text-sm text-gray-500">Loading font settings...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ToggleItem from '../ToggleItem.ts.vue';
import EditableFieldsSection from './EditableFieldsSection.ts.vue';
import { EmailEditor } from 'vue-email-editor';
import { reactive } from 'vue';
import ComponentJsonParser from './ComponentJsonParser';
import * as Utils from '../../../client-old/utils/Utils';
import * as OrganizationSettings from '../../services/organization-settings.js';

export default {
  name: 'ComponentEditModal',
  components: {
    ToggleItem,
    EditableFieldsSection,
    EmailEditor
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    componentMode: {
      type: String,
      default: 'override'
    },
    initialComponent: {
      type: Object,
      default: () => ({
        id: null,
        name: '',
        description: '',
        json: '',
        type: 'unlayer',
        editableFields: {},
        active: true
      })
    },
    editingComponent: {
      type: Boolean,
      default: false
    },
    globalComponents: {
      type: Array,
      default: () => []
    },
    orgId: {
      type: Number,
      required: true
    }
  },
  emits: ['close', 'save', 'component-select'],
  data() {
    return {
      component: { ...this.initialComponent },
      jsonError: '',
      parsedComponentJson: null,
      editableFields: reactive({}),
      detectedComponentTypes: [],
      componentTypesMap: {},
      componentNamesMap: {},
      isEditorReady: false,
      isSaving: false,
      designLoaded: false,
      customFonts: [],
      fontSettingsLoaded: false,
      emailTools: {
        enabled: true
      }
    };
  },
  watch: {
    initialComponent: {
      handler(newVal) {
        console.log('initialComponent changed:', newVal);
        this.component = { ...newVal };
        this.designLoaded = false; // Reset design loaded state
        this.parseComponentJson();

        // If editor is ready and we have JSON, load it
        if (this.isEditorReady && newVal.json) {
          setTimeout(() => {
            this.loadDesignIntoEditor();
          }, 100);
        }
      },
      deep: true
    },
    show(newVal) {
      if (newVal) {
        this.component = { ...this.initialComponent };
        this.jsonError = '';
        this.editableFields = reactive({});
        this.isEditorReady = false;
        this.designLoaded = false;
        this.fontSettingsLoaded = false;
        this.parseComponentJson();
        this.loadFontSettings();
      }
    },
    'component.json': {
      handler(newVal) {
        // When JSON changes, try to load it into the editor if editor is ready
        if (newVal && this.isEditorReady && !this.designLoaded) {
          this.loadDesignIntoEditor();
        }
      }
    }
  },
  computed: {
    groupedFieldPaths() {
      if (!this.parsedComponentJson) return {};

      const result = {};
      const allPaths = ComponentJsonParser.extractPaths(this.parsedComponentJson);

      allPaths.forEach(path => {
        const componentId = this.getComponentIdFromPath(path);
        if (componentId) {
          if (!result[componentId]) {
            result[componentId] = [];
          }
          result[componentId].push(path);
        }
      });

      return result;
    },
    editorOptions() {
      // Create a reactive computed property that updates when font settings change
      const customFonts = [];

      // Add all custom fonts from the new structure
      this.customFonts.forEach(fontFamily => {
        if (fontFamily.cssUrl && fontFamily.name) {
          const customFont = {
            label: fontFamily.name,
            value: fontFamily.cssValue || fontFamily.name.toLowerCase().replace(/\s+/g, '-'),
            url: fontFamily.cssUrl,
            weights: [
              { label: 'Thin', value: 100 },
              { label: 'Extra Light', value: 200 },
              { label: 'Light', value: 300 },
              { label: 'Regular', value: 400 },
              { label: 'Medium', value: 500 },
              { label: 'Semi Bold', value: 600 },
              { label: 'Bold', value: 700 },
              { label: 'Extra Bold', value: 800 },
              { label: 'Black', value: 900 }
            ]
          };
          customFonts.push(customFont);
        }
      });

      console.log('ComponentEditModal loaded custom fonts:', customFonts);

      const options = {
        displayMode: 'email',
        features: {
          preview: false,
          imageEditor: true,
          undoRedo: true,
          stockImages: false,
        },
        tools: {
          button: { enabled: true },
          divider: { enabled: true },
          heading: { enabled: true },
          html: { enabled: false },
          image: { enabled: true },
          menu: { enabled: false },
          social: { enabled: false },
          text: { enabled: true },
          timer: { enabled: false },
          video: { enabled: false },
          form: { enabled: false },
          row: { enabled: true }
        },
        fonts: {
          showDefaultFonts: true,
          customFonts: customFonts
        }
      };

      console.log('Editor options with fonts:', options.fonts);

      // Return clean, serializable options
      return JSON.parse(JSON.stringify(options));
    }
  },
  methods: {
    close() {
      try {
        console.log('Close button clicked - emitting close event');
        this.$emit('close');
      } catch (error) {
        console.error('Error in close method:', error);
        // Force close by directly emitting
        this.$emit('close');
      }
    },
    async save() {
      if (!this.isValidComponent) {
        return;
      }

      this.isSaving = true;

      try {
        // Get the latest design from the editor
        if (this.isEditorReady && this.$refs.emailEditor?.editor) {
          await new Promise((resolve) => {
            this.$refs.emailEditor.editor.saveDesign((design) => {
              this.component.json = JSON.stringify(design);
              this.parseComponentJson();
              resolve();
            });
          });
        }

        // Validate JSON
        try {
          JSON.parse(this.component.json);
        } catch (e) {
          this.jsonError = 'Invalid JSON structure';
          this.isSaving = false;
          return;
        }

        // Create a simple object mapping paths to descriptions
        const editableFieldsToSave = {};

        // Loop through all editable fields and extract those that are marked as editable
        Object.entries(this.editableFields).forEach(([path, data]) => {
          if (data && data.editable === true) {
            // Make sure we're not saving indices directly
            if (!path.match(/^\d+$/)) {
              // Save with the description as a string value
              editableFieldsToSave[path] = data.description || '';
            }
          }
        });

        // Prepare the component data for saving
        const componentData = {
          ...this.component,
          editableFields: editableFieldsToSave
        };

        this.$emit('save', componentData);
      } catch (error) {
        console.error('Error saving component:', error);
        this.jsonError = 'Failed to save component';
      } finally {
        this.isSaving = false;
      }
    },
    onEditorLoaded() {
      console.log('Unlayer editor loaded in component modal');
      this.isEditorReady = true;

      // Set up auto-save when design changes
      this.$refs.emailEditor.editor.addEventListener('design:updated', () => {
        if (this.designLoaded) {
          this.updateJsonFromEditor();
        }
      });

      // Try to load existing design if we have JSON
      if (this.component.json && !this.designLoaded) {
        // Use a small delay to ensure editor is fully initialized
        setTimeout(() => {
          this.loadDesignIntoEditor();
        }, 100);
      }
    },
    loadDesignIntoEditor() {
      if (!this.isEditorReady || !this.$refs.emailEditor?.editor || this.designLoaded) {
        return;
      }

      if (this.component.json) {
        try {
          console.log('Loading design into editor:', this.component.json.substring(0, 100) + '...');
          const design = JSON.parse(this.component.json);

          // Ensure we're passing a clean, cloneable object
          const cleanDesign = JSON.parse(JSON.stringify(design));

          this.$refs.emailEditor.editor.loadDesign(cleanDesign);
          this.designLoaded = true;

          console.log('Design loaded successfully into editor');
        } catch (error) {
          console.error('Error loading existing design:', error);
          this.jsonError = 'Failed to load existing design into editor';
        }
      }
    },
    updateJsonFromEditor() {
      if (!this.$refs.emailEditor?.editor) return;

      try {
        this.$refs.emailEditor.editor.saveDesign((design) => {
          try {
            this.component.json = JSON.stringify(design, null, 2);
            this.parseComponentJson();
            console.log('JSON updated from editor, new editable fields count:', Object.keys(this.editableFields).length);
          } catch (error) {
            console.error('Error updating JSON from editor:', error);
            this.jsonError = 'Failed to update JSON from editor';
          }
        });
      } catch (error) {
        console.error('Error calling saveDesign:', error);
      }
    },
    async loadFontSettings() {
      try {
        // Load custom fonts from organization settings
        const customFontsString = await OrganizationSettings.getOrganizationSetting('customFonts');
        if (customFontsString) {
          try {
            this.customFonts = JSON.parse(customFontsString);
            console.log('ComponentEditModal loaded custom fonts:', this.customFonts);
          } catch (error) {
            console.error('Error parsing custom fonts:', error);
            this.customFonts = [];
          }
        } else {
          this.customFonts = [];
        }

        console.log('Font settings loaded - computed editorOptions will update automatically');
        this.fontSettingsLoaded = true;
      } catch (error) {
        console.error('Error loading font settings:', error);
        this.fontSettingsLoaded = true; // Still allow editor to load
      }
    },
    onComponentSelect() {
      this.$emit('component-select', this.component.id);
    },
    parseComponentJson() {
      try {
        const json = this.component.json;
        if (!json) {
          this.parsedComponentJson = null;
          this.detectedComponentTypes = [];
          return;
        }

        this.parsedComponentJson = JSON.parse(json);
        this.detectComponentTypes();

        // Always update editableFields to include new paths
        const allPaths = ComponentJsonParser.extractPaths(this.parsedComponentJson);
        const editableFieldsEmpty = Object.keys(this.editableFields).length === 0;

        // For each path found in the JSON, ensure it exists in editableFields
        allPaths.forEach(path => {
          if (!this.editableFields[path]) {
            // Add new paths that don't exist yet
            this.editableFields[path] = { editable: false, description: '' };
          }
        });

        // Remove paths that no longer exist in the JSON (cleanup)
        const currentPaths = Object.keys(this.editableFields);
        currentPaths.forEach(path => {
          if (!allPaths.includes(path)) {
            delete this.editableFields[path];
          }
        });

        // If we're editing a component with existing editable fields, initialize them
        // Only do this when first loading (empty state)
        if (editableFieldsEmpty && this.editingComponent && this.component.editableFields) {
          const fields = typeof this.component.editableFields === 'string'
            ? JSON.parse(this.component.editableFields)
            : this.component.editableFields;

          Object.keys(fields).forEach(key => {
            if (this.editableFields[key]) {
              this.editableFields[key].editable = true;

              // Handle both string descriptions and object values (for color fields)
              if (typeof fields[key] === 'string') {
                this.editableFields[key].description = fields[key];
              } else if (typeof fields[key] === 'object' && fields[key] !== null) {
                // For backward compatibility with color fields that have both description and value
                if (fields[key].description) {
                  this.editableFields[key].description = fields[key].description;
                }
              }
            }
          });
        }
      } catch (e) {
        console.error('Error parsing component JSON:', e);
        this.parsedComponentJson = null;
        this.detectedComponentTypes = [];
        this.jsonError = 'Invalid JSON structure';
      }
    },
    detectComponentTypes() {
      this.detectedComponentTypes = [];
      if (!this.parsedComponentJson || !this.parsedComponentJson.body || !this.parsedComponentJson.body.rows) {
        return;
      }

      try {
        // Create Row-based groupings instead of individual component groupings
        this.parsedComponentJson.body.rows.forEach((row, rowIndex) => {
          const rowId = `row-${rowIndex}`;
          
          // Set row group name and type
          this.componentNamesMap[rowId] = `Row ${rowIndex + 1}`;
          this.componentTypesMap[rowId] = 'row';

          // Still track individual component types for reference
          if (row.columns) {
            row.columns.forEach((column, colIndex) => {
              if (column.contents) {
                column.contents.forEach(content => {
                  if (content.type && !this.detectedComponentTypes.includes(content.type)) {
                    this.detectedComponentTypes.push(content.type);
                  }
                });
              }
            });
          }
        });
      } catch (e) {
        console.error('Error detecting component types:', e);
      }
    },
    getComponentIdFromPath(path) {
      // Group all fields by Row (row-level, column-level, and component-level fields all go into the same row group)
      const rowMatch = path.match(/body\.rows\[(\d+)\]/);
      if (rowMatch) {
        const rowIndex = parseInt(rowMatch[1]);
        return `row-${rowIndex}`;
      }

      return null;
    },
    isValidComponent() {
      return this.component.name && this.component.name.trim() !== '';
    }
  }
};
</script>
