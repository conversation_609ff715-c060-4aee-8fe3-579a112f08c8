<template>
  <div class="mt-4">
    <label class="block text-sm font-medium text-gray-700 mb-1">Editable AI Fields</label>
    <div class="border border-gray-300 rounded-md p-3 h-64 overflow-y-auto">
      <p class="text-sm text-gray-500 mb-2">Select fields that can be edited by AI and provide descriptions:</p>

      <!-- Loop through component groups -->
      <div v-for="(paths, componentId) in groupedFieldPaths" :key="componentId" class="mb-4">
        <div class="font-medium text-gray-800 mb-1">
          {{ getComponentName(componentId) }}
          <span class="text-xs text-gray-500">({{ getComponentType(componentId) }})</span>
        </div>

        <!-- Field checkboxes with descriptions -->
        <div v-for="path in paths" :key="path" class="ml-2 mb-2">
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input :id="`field-${path}`"
                type="checkbox"
                v-model="editableFields[path].editable"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
            </div>
            <div class="ml-2 w-full">
              <label :for="`field-${path}`" class="text-sm font-medium text-gray-700">
                {{ getFieldLabel(path) }}
              </label>
              <div class="mt-1">
                <input type="text"
                      v-model="editableFields[path].description"
                      :disabled="!editableFields[path].editable"
                      :placeholder="getDefaultColorPlaceholder(path)"
                      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Message when no fields are detected -->
      <div v-if="!groupedFieldPaths || Object.keys(groupedFieldPaths).length === 0" class="text-sm text-gray-500">
        No editable fields detected. Make sure your JSON contains valid Unlayer components.
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditableFieldsSection',
  props: {
    editableFields: {
      type: Object,
      default: () => ({})
    },
    groupedFieldPaths: {
      type: Object,
      default: () => ({})
    },
    componentTypesMap: {
      type: Object,
      default: () => ({})
    },
    componentNamesMap: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getComponentName(componentId) {
      return (this.componentNamesMap && this.componentNamesMap[componentId]) || componentId;
    },
    getComponentType(componentId) {
      return (this.componentTypesMap && this.componentTypesMap[componentId]) || 'unknown';
    },
    getFieldLabel(path) {
      let componentType = 'unknown';
      if (this.componentTypesMap) {
        for (const [contentId, type] of Object.entries(this.componentTypesMap)) {
          if (path.includes(contentId)) {
            componentType = type;
            break;
          }
        }
      }

      // Handle text fields with component context
      if (path.endsWith('.values.text')) {
        if (componentType === 'heading') return 'Heading Text';
        else if (componentType === 'text') return 'Text Content';
        else if (componentType === 'button') return 'Button Text';
        else return 'Text';
      } 
      
      // Handle other specific field types
      else if(path.endsWith('.values.action.values.href')) {
        return 'Image Link URL';
      } else if (path.endsWith('.values.src.url')) {
        return 'Image URL';
      } else if (path.endsWith('.values.altText')) {
        return 'Alt Text';
      } else if (path.endsWith('.values.href.values.href')) {
        return 'Button URL';
      } else if (path.endsWith('.fontFamily')) {
        return 'Font Family';
      }
      
      // Handle color fields with CONTEXTUAL LABELS based on path structure
      else if (path.endsWith('.color')) {
        // Generic color - determine context from path
        if (path.includes('contents[') && componentType === 'heading') {
          return 'Heading Text Color';
        } else if (path.includes('contents[') && componentType === 'text') {
          return 'Text Color';
        } else if (path.includes('contents[') && componentType === 'button') {
          return 'Button Text Color';
        } else if (path.includes('contents[') && componentType === 'image') {
          return 'Image Text Color';
        } else {
          return 'Text Color';
        }
      } 
      else if (path.endsWith('.backgroundColor')) {
        // Generic backgroundColor - determine context from path structure
        // Check more specific patterns first to avoid false matches
        if (path.includes('contents[') && path.includes('.values.backgroundColor')) {
          // This is a component-level backgroundColor
          if (componentType === 'heading') return 'Heading Background Color';
          else if (componentType === 'text') return 'Text Background Color';
          else if (componentType === 'button') return 'Button Background Color';
          else if (componentType === 'image') return 'Image Background Color';
          else return 'Component Background Color';
        } else if (path.includes('columns[') && path.includes('].values.backgroundColor') && !path.includes('contents[')) {
          // This is specifically a column-level backgroundColor (not a component inside the column)
          return 'Column Background Color';
        } else if (path.includes('body.rows[') && path.includes('].values.backgroundColor') && !path.includes('columns[')) {
          // This is specifically a row-level backgroundColor (not a column or component inside the row)
          return 'Row Background Color';
        } else {
          return 'Background Color';
        }
      } 
      else if (path.endsWith('.textColor')) {
        return 'Text Color';
      } else if (path.endsWith('.linkColor')) {
        return 'Link Color';
      } else if (path.endsWith('.columnsBackgroundColor')) {
        return 'Columns Background Color';
      } 
      
      // Handle nested button color fields (these already have good context)
      else if (path.endsWith('.buttonColors.color')) {
        return 'Button Text Color';
      } else if (path.endsWith('.buttonColors.backgroundColor')) {
        return 'Button Background Color';
      } else if (path.endsWith('.buttonColors.hoverColor')) {
        return 'Button Hover Text Color';
      } else if (path.endsWith('.buttonColors.hoverBackgroundColor')) {
        return 'Button Hover Background Color';
      } 
      
      // Handle nested link style fields
      else if (path.endsWith('.linkStyle.linkColor')) {
        return 'Link Color';
      } else if (path.endsWith('.linkStyle.linkHoverColor')) {
        return 'Link Hover Color';
      } 
      
      // Fallback for unknown fields
      else {
        const lastPart = path.split('.').pop() || '';
        return lastPart.charAt(0).toUpperCase() + lastPart.slice(1);
      }
    },
    getDefaultColorPlaceholder(path) {
      // Provide descriptive placeholders for color fields with context
      if (path.endsWith('.color')) {
        if (path.includes('contents[')) {
          return 'Describe what color to use for this text (e.g., "Use brand red #d72323")';
        } else {
          return 'Describe what color to use (e.g., "Use brand red #d72323")';
        }
      } else if (path.endsWith('.backgroundColor')) {
        if (path.includes('columns[') && path.includes('].values.backgroundColor')) {
          return 'Describe the column background color (e.g., "Use brand red #982f2f")';
        } else if (path.includes('body.rows[') && path.includes('].values.backgroundColor')) {
          return 'Describe the row background color (e.g., "Use light gray #f5f5f5")';
        } else if (path.includes('contents[')) {
          return 'Describe the component background color (e.g., "Use white #ffffff")';
        } else {
          return 'Describe what background color to use (e.g., "Use brand black #000000")';
        }
      } else if (path.endsWith('.textColor')) {
        return 'Describe what text color to use (e.g., "Use brand blue #1c2545")';
      } else if (path.endsWith('.linkColor')) {
        return 'Describe what link color to use (e.g., "Use brand blue #1155cc")';
      } else if (path.endsWith('.columnsBackgroundColor')) {
        return 'Describe what columns background color to use (e.g., "Use white #ffffff")';
      } else if (path.endsWith('.buttonColors.color')) {
        return 'Describe the button text color (e.g., "Use white #ffffff for contrast")';
      } else if (path.endsWith('.buttonColors.backgroundColor')) {
        return 'Describe the button background color (e.g., "Use brand blue #3AAEE0")';
      } else if (path.endsWith('.buttonColors.hoverColor')) {
        return 'Describe the button text color on hover (e.g., "Keep white #ffffff")';
      } else if (path.endsWith('.buttonColors.hoverBackgroundColor')) {
        return 'Describe the button background color on hover (e.g., "Darker blue #2A8EC0")';
      } else if (path.endsWith('.linkStyle.linkColor')) {
        return 'Describe the link color (e.g., "Use brand blue #1155cc")';
      } else if (path.endsWith('.linkStyle.linkHoverColor')) {
        return 'Describe the link color on hover (e.g., "Use darker blue #0000ee")';
      } else if (path.endsWith('.fontFamily')) {
        return 'Describe what font family to use (e.g., "Use Arial for readability")';
      }
      return 'Field description (optional)';
    }
  }
};
</script>
