<template>
	<div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
	  <table class="min-w-full divide-y divide-gray-200">
		<thead class="bg-gray-50">
		  <tr>
			<th scope="col" class="px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3 2xl:w-auto">Campaign Name</th>
			<th scope="col" class="px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
			<th scope="col" class="hidden 2xl:table-cell px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider">Target Segment</th>
			<th scope="col" class="px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
			<th scope="col" class="hidden lg:table-cell px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
			<th scope="col" class="px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
			<th scope="col" class="px-3 py-2 2xl:px-6 2xl:py-3 text-left text-[11px] 2xl:text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
		  </tr>
		</thead>
		<tbody class="bg-white divide-y divide-gray-200">
		  <tr v-for="campaign in campaigns" :key="campaign.id"
		 :data-campaign-id="campaign.id"
		 :class="{
		   'bg-red-50': isArchived(campaign.id),
		   'hover:bg-gray-50': !isArchived(campaign.id) && !inProgress,
		   'highlight-new-campaign': highlightedCampaigns.has(campaign.id)
		 }">

			<!-- Campaign Name -->
			<td class="px-3 py-2 2xl:px-6 2xl:py-4 whitespace-normal 2xl:whitespace-nowrap w-1/3 2xl:w-auto">
			  <div class="group flex items-center">
				<div class="mr-1 2xl:mr-2">
				  <svg v-if="campaign.taskType === 'Email'"
					class="h-3 w-3 2xl:h-4 2xl:w-4 text-purple-600" viewBox="0 0 24 24"
					fill="currentColor">
					<path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4-8 5-8-5V6l8 5 8-5v2z" />
				  </svg>
				  <svg v-else-if="campaign.taskType === 'SMS'"
					class="h-3 w-3 2xl:h-4 2xl:w-4 text-purple-600" viewBox="0 0 24 24"
					fill="currentColor">
					<path d="M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H4V4h16v16zM7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2zm-6 4h8v2H9z" />
				  </svg>
				  <svg v-else-if="campaign.taskType === 'Loyalty'"
					class="h-3 w-3 2xl:h-4 2xl:w-4 text-purple-600" viewBox="0 0 24 24"
					fill="currentColor">
					<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
				  </svg>
				</div>

				<template v-if="!isEditing('name', campaign.id)">
				  <div @click.stop="!inProgress && onEdit('name', campaign.id)"
					class="font-medium text-sm 2xl:text-base text-gray-900"
					:class="{'cursor-pointer': !inProgress}">
					{{ campaign.name }}
				  </div>
				</template>
				<input v-else v-model="editFields.name" @blur="onSave('name', campaign.id)"
				  @keyup.enter="onSave('name', campaign.id)"
				  style="width: 384px !important; min-width: 384px !important;"
				  class="font-medium text-gray-900 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0 w-96 min-w-[24rem]"
				  ref="nameInput">
			  </div>

			  <!-- Description tooltip on hover -->
			  <div class="mt-1 text-xs 2xl:text-sm text-gray-500 truncate max-w-md 2xl:max-w-xl"
				title="Click to edit"
				@click.stop="!inProgress && onEdit('description', campaign.id)"
				v-if="!isEditing('description', campaign.id) && !isArchived(campaign.id)">
				{{ campaign.description.length > 60 ? campaign.description.substring(0, 60) + '...' : campaign.description }}
			  </div>
			  <textarea v-else-if="isEditing('description', campaign.id)" v-model="editFields.description"
				@blur="onSave('description', campaign.id)"
				@keyup.enter="onSave('description', campaign.id)"
				class="w-full text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0"
				rows="3" ref="descriptionInput"></textarea>
			</td>

			<!-- Campaign Type -->
			<td class="px-3 py-2 2xl:px-6 2xl:py-4 whitespace-nowrap">
			  <div v-if="!isEditing('type', campaign.id)"
				@click.stop="!inProgress && onEdit('type', campaign.id)"
				class="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] 2xl:text-xs font-medium"
				:class="{
				  'bg-green-100 text-green-800': campaign.type === 'Promotion',
				  'bg-blue-100 text-blue-800': campaign.type === 'Education',
				  'bg-orange-100 text-orange-800': campaign.type === 'Awareness',
				  'cursor-pointer': !inProgress
				}">
				{{ campaign.type }}
			  </div>
			  <select v-else v-model="editFields.type"
				@blur="onSave('type', campaign.id)"
				@keyup.enter="onSave('type', campaign.id)"
				class="text-xs font-medium rounded-full focus:outline-none focus:ring-0"
				:class="{
				  'bg-green-100 text-green-600': editFields.type === 'Promotion',
				  'bg-blue-50 text-blue-700': editFields.type === 'Education',
				  'bg-orange-50 text-orange-700': editFields.type === 'Awareness'
				}">
				<option value="Promotion">Promotion</option>
				<option value="Education">Education</option>
				<option value="Awareness">Awareness</option>
			  </select>
			</td>

			<!-- Target Segment -->
			<td class="hidden 2xl:table-cell px-3 py-2 2xl:px-6 2xl:py-4 whitespace-nowrap">
			  <div v-if="!isEditing('targetSegment', campaign.id)"
				@click.stop="!inProgress && onEdit('targetSegment', campaign.id)"
				:class="{'cursor-pointer': !inProgress}"
				class="text-xs 2xl:text-sm text-gray-900">
				{{ campaign.targetSegment }}
			  </div>
			  <input v-else v-model="editFields.targetSegment"
				@blur="onSave('targetSegment', campaign.id)"
				@keyup.enter="onSave('targetSegment', campaign.id)"
				class="text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0"
				ref="targetSegmentInput">
			</td>

			<!-- Date -->
			<td class="px-3 py-2 2xl:px-6 2xl:py-4 whitespace-nowrap">
			  <div v-if="!isEditing('scheduledDate', campaign.id)"
				@click.stop="!inProgress && onEdit('scheduledDate', campaign.id)"
				:class="{'cursor-pointer': !inProgress}"
				class="text-xs 2xl:text-sm text-gray-900">
				{{ formatDate(campaign.scheduledDate) }}
			  </div>
			  <input v-else type="date" v-model="editFields.scheduledDate"
				@blur="onSave('scheduledDate', campaign.id)"
				@keyup.enter="onSave('scheduledDate', campaign.id)"
				class="text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded border-none focus:outline-none focus:ring-0"
				ref="scheduledDateInput">
			</td>

			<!-- Created By -->
			<td class="hidden lg:table-cell px-3 py-2 2xl:px-6 2xl:py-4 whitespace-nowrap">
			  <div class="text-xs 2xl:text-sm text-gray-900">
				{{ campaign.createdByName || 'Unknown' }}
			  </div>
			</td>

			<!-- Status -->
			<td class="px-3 py-2 2xl:px-6 2xl:py-4 whitespace-nowrap">
			  <span v-if="isArchived(campaign.id)" class="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] 2xl:text-xs font-medium bg-red-100 text-red-800">
				Skipped
			  </span>
			  <span v-else-if="inProgress && campaign.taskStatus === 'Processing'" class="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] 2xl:text-xs font-medium bg-yellow-100 text-yellow-800">
				Generating
			  </span>
			  <span v-else-if="inProgress" class="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] 2xl:text-xs font-medium" :style="getStatusStyle(campaign.taskStatus)">
				<div class="w-2 h-2 mr-1.5 rounded-sm" :style="{ backgroundColor: getStatusColor(campaign.taskStatus) }"></div>
				{{ getStatusDisplayText(campaign.taskStatus) }}
			  </span>
			  <span v-else class="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] 2xl:text-xs font-medium bg-gray-100 text-gray-800">
				Planning
			  </span>
			</td>

			<!-- Actions -->
                        <td class="px-3 py-2 2xl:px-6 2xl:py-4 whitespace-nowrap text-right text-xs 2xl:text-sm font-medium">
                          <div v-if="!isArchived(campaign.id)" class="relative campaign-action-menu flex justify-end items-center gap-2">
                                <button v-if="inProgress && campaign.taskStatus !== 'Processing'"
                                      @click.stop="$emit('navigate', campaign.id)"
                                      class="text-blue-600 hover:text-blue-800 text-xs 2xl:text-sm font-medium">
                                      View
                                </button>
                                <button @click.stop="toggleActionMenu(campaign.id)" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                  </svg>
                                </button>

                                <div v-if="activeActionMenu === campaign.id" class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg z-20 py-1 border border-gray-200">
                                  <button
                                        @click.stop="$emit('delete', campaign.id); toggleActionMenu(null)"
                                        class="block w-full text-left px-4 py-2 text-xs 2xl:text-sm text-red-600 hover:bg-red-50">
                                        Delete
                                  </button>
                                </div>
                          </div>
                        </td>
		  </tr>
		</tbody>
	  </table>
	</div>
  </template>

  <script>
  export default {
	name: 'CampaignTable',

	props: {
	  campaigns: {
		type: Array,
		required: true
	  },
	  inProgress: {
		type: Boolean,
		default: false
	  },
	  archivedCampaigns: {
		type: Set,
		required: true
		 },
		 highlightedCampaigns: {
		type: Set,
		required: true
		 }
	},

	data() {
	  return {
		editingField: null,
		editingCampaignId: null,
                editFields: {
                  name: '',
                  description: '',
                  type: '',
                  targetSegment: '',
                  scheduledDate: ''
                },
                activeActionMenu: null,
                statusOptions: [
                  { label: 'Campaign Ready', value: 'Campaign Ready', color: '#6366F1' }, // Indigo
                  { label: 'Ready for Copywriting', value: 'Ready for Copywriting', color: '#8B5CF6' }, // Purple
                  { label: 'In Copywriting', value: 'In Copywriting', color: '#A78BFA' }, // Light purple
                  { label: 'Ready for Design', value: 'Ready for Design', color: '#EC4899' }, // Pink
                  { label: 'In Design', value: 'In Design', color: '#F472B6' }, // Light pink
                  { label: 'Quality Check', value: 'Quality Check', color: '#F59E0B' }, // Amber
                  { label: 'Ready for Review', value: 'Ready for Review', color: '#10B981' }, // Emerald
                  { label: 'In Review', value: 'In Review', color: '#34D399' }, // Green
                  { label: 'Approved', value: 'Approved', color: '#3B82F6' }, // Blue
                  { label: 'Done', value: 'Complete', color: '#059669' } // Green
                ]
          }
        },

        mounted() {
          document.addEventListener('click', this.handleOutsideClick);
        },

        beforeUnmount() {
          document.removeEventListener('click', this.handleOutsideClick);
        },

        methods: {
	  isArchived(campaignId) {
		return this.archivedCampaigns.has(campaignId);
	  },

	  isEditing(field, campaignId) {
		return this.editingField === field && this.editingCampaignId === campaignId;
	  },

	  onEdit(field, campaignId) {
		if (this.inProgress) return;

		const campaign = this.campaigns.find(c => c.id === campaignId);
		if (!campaign) return;

		this.editingField = field;
		this.editingCampaignId = campaignId;

		// Set edit value based on field type
		if (field === 'scheduledDate') {
		  // For date editing, handle format conversion
		  if (typeof campaign.scheduledDate === 'string' && campaign.scheduledDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
			this.editFields.scheduledDate = campaign.scheduledDate;
		  } else if (typeof campaign.scheduledDate === 'string' && campaign.scheduledDate.includes('T')) {
			this.editFields.scheduledDate = campaign.scheduledDate.split('T')[0];
		  } else {
			// Create a UTC date and format carefully
			try {
			  const date = new Date(campaign.scheduledDate);
			  const year = date.getUTCFullYear();
			  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
			  const day = String(date.getUTCDate()).padStart(2, '0');
			  this.editFields.scheduledDate = `${year}-${month}-${day}`;
			} catch (error) {
			  console.error('Error processing date for edit:', error);
			  // Fallback
			  const date = new Date(campaign.scheduledDate);
			  this.editFields.scheduledDate = date.toISOString().split('T')[0];
			}
		  }
		} else {
		  this.editFields[field] = campaign[field] || '';
		}

		// Focus the input field
		this.$nextTick(() => {
		  if (this.$refs[`${field}Input`]) {
			this.$refs[`${field}Input`].focus();
		  }
		});
	  },

	  onSave(field, campaignId) {
		if (!this.editFields[field]) {
		  this.editingField = null;
		  this.editingCampaignId = null;
		  return;
		}

		this.$emit('edit-field', {
		  field,
		  campaignId,
		  value: this.editFields[field]
		});

		this.editingField = null;
		this.editingCampaignId = null;
	  },

	  formatDate(dateString) {
		if (!dateString) return '';

		// The direct string parsing approach prevents timezone issues
		if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
		  // Extract year, month, day directly from string
		  const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));

		  // Map month number to month name (avoiding Date object timezone issues)
		  const monthNames = [
			"January", "February", "March", "April", "May", "June",
			"July", "August", "September", "October", "November", "December"
		  ];

		  // Format directly without using Date object
		  return `${monthNames[month-1]} ${day}, ${year}`;
		}

		// Fallback to UTC-based Date object for other date formats
		try {
		  // Create a UTC date to avoid timezone shifts
		  const date = new Date(dateString);
		  // Force UTC timezone display
		  return date.toLocaleDateString('en-US', {
			month: 'long',
			day: 'numeric',
			year: 'numeric',
			timeZone: 'UTC' // Force UTC timezone
		  });
		} catch (error) {
		  console.error('Error formatting date in CampaignTable:', error, dateString);
		  return dateString; // Return original string as fallback
		}
	  },

	  getStatusColor(status) {
		// For backward compatibility with old statuses
		if (status === 'Ready') return '#9CA3AF'; // Gray for Not Started
		if (status === 'In Progress') return '#6366F1'; // Indigo
		if (status === 'Complete') return '#059669'; // Green

		// For new status format
		const option = this.statusOptions.find(opt => opt.value === status);
		return option ? option.color : '#9CA3AF'; // Default gray color if not found
	  },

	  getStatusStyle(status) {
		const color = this.getStatusColor(status);
		// Create a lighter background color based on the status color
		return {
		  backgroundColor: `${color}15`, // 15% opacity version of the color
		  color: color
		};
	  },

          getStatusDisplayText(status) {
                if (!status) return 'Not Started';

		// For backward compatibility with old statuses
		if (status === 'Ready') return 'Not Started';
		if (status === 'In Progress') return 'In Progress';
		if (status === 'Complete') return 'Done';

		// For new status format
                const option = this.statusOptions.find(opt => opt.value === status);
                return option ? option.label : status;
          },

          toggleActionMenu(campaignId) {
                if (this.activeActionMenu === campaignId) {
                  this.activeActionMenu = null;
                } else {
                  this.activeActionMenu = campaignId;
                }
          },

          handleOutsideClick(event) {
                if (
                  this.activeActionMenu !== null &&
                  !event.target.closest('.campaign-action-menu')
                ) {
                  this.activeActionMenu = null;
                }
          }
        }
  }
  </script>

  <style scoped>
  select {
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	background-color: transparent;
	border: none;
	padding: 0.25rem 0.5rem;
	cursor: pointer;
  }

  select:focus {
	outline: none;
	box-shadow: none;
  }
  </style>
