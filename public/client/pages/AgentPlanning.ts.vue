<template>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable">
		<img src="../images/ai_strategist.jpg" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">AI Marketing Strategist</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Our AI Marketing Strategist helps you create data-driven marketing plans and campaigns tailored to your brand and audience.
		</p>
		<PrimaryButton
			cta="Upgrade Loyalty Plan"
			size="xs"
			@click="() => this.$router.push('/loyalty/settings/plans')"
		/>
	</div>

	<div v-if="isFeatureAvailable" class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">
		<div>
			<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
				Command Center
			</div>
			<p class="mt-1 text-gray-500">Work with your in-house AI Marketing Strategist to create plans for your brand.</p>
		</div>
	</div>

	<div v-if="isFeatureAvailable" class="min-h-screen bg-[#F5F5F5] p-4 pt-3">
		<!-- Tabs -->
		<div class="border-b border-gray-300 mb-3">
			<div class="flex space-x-8">
				<!-- Campaigns Tab -->
				<div class="whitespace-nowrap flex items-center py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
					@click.stop="showCampaigns = true; showPlans = false"
					:class="{'text-ralprimary-light border-ralprimary-light border-b-2': showCampaigns}">
					Campaigns
					<div class="flex items-center justify-center min-w-[20px] h-5 ml-2 px-1.5 text-xs text-center font-semibold rounded-full bg-[#32316A] text-white">
						{{ campaignsCount }}
					</div>
				</div>

				<!-- Plans Tab -->
				<div class="whitespace-nowrap flex items-center py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
					@click.stop="showCampaigns = false; showPlans = true"
					:class="{'text-ralprimary-light border-ralprimary-light border-b-2': showPlans}">
					Plans
					<div class="flex items-center justify-center min-w-[20px] h-5 ml-2 px-1.5 text-xs text-center font-semibold rounded-full bg-[#32316A] text-white">
						{{ plansCount }}
					</div>
				</div>
			</div>
		</div>

		<!-- Campaigns Tab Content -->
		<div v-if="showCampaigns" class="mt-4">
			<!-- Filters and Actions -->
			<div class="flex flex-wrap gap-4 mb-6 justify-between">
				<div class="flex flex-wrap gap-4">
				<!-- Filter button -->
				<div class="relative">
					<button
						@click="toggleFilterDropdown"
						class="filter-button inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
						:class="{'ring-2 ring-indigo-500 border-indigo-500': hasActiveFilters}"
					>
						<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
						</svg>
						Filters
						<span v-if="activeFilterCount > 0" class="ml-1.5 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
							{{ activeFilterCount }}
						</span>
					</button>

					<!-- Filter dropdown panel -->
					<div v-if="showFilterDropdown" class="filter-dropdown absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg z-10 p-4 border border-gray-200">
						<h3 class="text-sm font-medium text-gray-900 mb-3">Filter Campaigns</h3>

						<!-- Campaign Type Filter -->
						<div class="mb-4">
							<label class="block text-sm font-medium text-gray-700 mb-1">Campaign Type</label>
							<div class="flex flex-wrap gap-2">
								<button
									@click="selectType('')"
									class="px-3 py-1 text-xs rounded-full border"
									:class="selectedType === '' ? 'bg-indigo-100 border-indigo-300 text-indigo-800' : 'border-gray-300 text-gray-700'"
								>
									All
								</button>
								<button
									@click="selectType('Promotion')"
									class="px-3 py-1 text-xs rounded-full border flex items-center"
									:class="selectedType === 'Promotion' ? 'bg-green-100 border-green-300 text-green-800' : 'border-gray-300 text-gray-700'"
								>
									<span v-if="selectedType === 'Promotion'" class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
									Promotion
								</button>
								<button
									@click="selectType('Education')"
									class="px-3 py-1 text-xs rounded-full border flex items-center"
									:class="selectedType === 'Education' ? 'bg-blue-100 border-blue-300 text-blue-800' : 'border-gray-300 text-gray-700'"
								>
									<span v-if="selectedType === 'Education'" class="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
									Education
								</button>
								<button
									@click="selectType('Awareness')"
									class="px-3 py-1 text-xs rounded-full border flex items-center"
									:class="selectedType === 'Awareness' ? 'bg-orange-100 border-orange-300 text-orange-800' : 'border-gray-300 text-gray-700'"
								>
									<span v-if="selectedType === 'Awareness'" class="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
									Awareness
								</button>
							</div>
						</div>

						<!-- Status Filter -->
						<div class="mb-4">
							<label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
							<select
								v-model="selectedStatus"
								class="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md"
							>
								<option value="">All Statuses</option>
								<option v-for="status in statusOptions" :key="status.value" :value="status.value">
									{{ status.label }}
								</option>
							</select>
						</div>

						<!-- Plan Filter -->
						<div class="mb-4">
							<label class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
							<select
								v-model="selectedPlan"
								class="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md"
							>
								<option value="">All Plans</option>
								<option v-for="plan in availablePlans" :key="plan.versionId" :value="plan.versionId">
									{{ plan.name }}
								</option>
							</select>
						</div>

						<!-- Date Range Filter -->
						<div class="mb-4">
							<label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
							<div class="grid grid-cols-2 gap-2">
								<div>
									<label class="block text-xs text-gray-500 mb-1">Start Date</label>
									<input
										type="date"
										v-model="dateFilter.startDate"
										class="block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
									/>
								</div>
								<div>
									<label class="block text-xs text-gray-500 mb-1">End Date</label>
									<input
										type="date"
										v-model="dateFilter.endDate"
										class="block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
									/>
								</div>
							</div>
						</div>

						<!-- Show Archived Toggle -->
						<div class="mb-4">
							<div class="flex items-center">
								<input
									type="checkbox"
									id="showArchived"
									v-model="showArchived"
									@change="onArchivedToggleChange"
									class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
								/>
								<label for="showArchived" class="ml-2 block text-sm text-gray-700">
									Show archived plans and campaigns
								</label>
							</div>
						</div>

						<!-- Filter Actions -->
						<div class="flex justify-between">
							<button
								@click="clearAllFilters"
								class="text-sm text-gray-600 hover:text-gray-900"
							>
								Clear all filters
							</button>
							<button
								@click="showFilterDropdown = false"
								class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
							>
								Apply Filters
							</button>
						</div>
					</div>
				</div>
				<!-- Search input -->
				<div class="relative flex-grow max-w-md">
					<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
						</svg>
					</div>
					<input
						type="text"
						v-model="searchQuery"
						placeholder="Search by campaign or plan name"
						class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
					>
				</div>
				</div>

				<!-- Bulk Actions -->
				<div class="flex items-center gap-3">
					<!-- Selection count -->
					<div v-if="selectedCampaigns.length > 0" class="text-sm text-gray-500">
						{{ selectedCampaigns.length }} {{ selectedCampaigns.length === 1 ? 'campaign' : 'campaigns' }} selected
					</div>
					<!-- Bulk Status Update -->
					<div class="relative">
						<button
							@click="toggleBulkStatusDropdown"
							:disabled="selectedCampaigns.length === 0"
							data-dropdown="bulk-status"
							class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-60 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
							</svg>
							Update Status
						</button>
						<!-- Status dropdown -->
						<div v-if="showBulkStatusDropdown" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
							<div class="py-1">
								<div class="px-4 py-2 text-xs text-gray-500 uppercase tracking-wider font-semibold">Select Status</div>
								<button
									v-for="status in statusOptions"
									:key="status.value"
									@click="confirmBulkStatusUpdate(status.value)"
									class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
								>
									<div class="flex items-center">
										<span class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: status.color }"></span>
										{{ status.label }}
									</div>
								</button>
							</div>
						</div>
					</div>

					<!-- Bulk Delete -->
					<button
						@click="confirmBulkDelete"
						:disabled="selectedCampaigns.length === 0"
						class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-60 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
						</svg>
						Delete
					</button>
				</div>
			</div>

			<!-- Plan Filter Indicator -->
			<div v-if="$route.query.plan" class="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg flex items-center justify-between">
				<div class="flex items-center">
					<svg class="h-5 w-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
					</svg>
					<span class="text-sm text-purple-800 font-medium">
						Showing campaigns from your newly created plan
					</span>
				</div>
				<button @click="$router.push('/ai-strategist/planning')" class="text-sm text-purple-600 hover:text-purple-800 underline">
					Show all campaigns
				</button>
			</div>

			<!-- Loading skeleton -->
			<div v-if="!campaignsLoaded" class="space-y-4">
				<div v-for="i in 3" :key="i" class="bg-white rounded-lg p-4 border border-gray-200 animate-pulse">
					<div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
					<div class="h-4 bg-gray-200 rounded w-1/2"></div>
				</div>
			</div>

			<!-- Campaign table -->
			<div v-else-if="campaignsLoaded && filteredCampaigns.length > 0" class="bg-white shadow overflow-hidden sm:rounded-md">
				<!-- Responsive table container with horizontal scrolling for small screens -->
                                <div class="overflow-x-auto">
                                        <table class="min-w-[900px] w-full divide-y divide-gray-200 table-fixed">
						<thead class="bg-gray-50">
							<tr>
								<!-- Checkbox column -->
                                                                <th scope="col" class="hidden sm:table-cell w-[5%] px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									<div class="flex items-center">
										<input
											type="checkbox"
											@click="toggleSelectAll"
											:checked="isAllSelected"
											class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
										>
									</div>
								</th>
								<!-- Campaign name - flexible width but will shrink -->
                                                                <th scope="col" class="w-auto sm:w-[18%] px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="setSortField('name')">
									<div class="flex items-center">
										Campaign Name
										<span v-if="sortField === 'name'" class="ml-1">
											<svg v-if="sortOrder === 'asc'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
											</svg>
											<svg v-else class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
											</svg>
										</span>
									</div>
								</th>
								<!-- Type - small fixed width -->
                                                                <th scope="col" class="hidden sm:table-cell w-[15%] px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="setSortField('type')">
									<div class="flex items-center">
										Type
										<span v-if="sortField === 'type'" class="ml-1">
											<svg v-if="sortOrder === 'asc'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
											</svg>
											<svg v-else class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
											</svg>
										</span>
									</div>
								</th>
								<!-- Target - will shrink -->
                                                                <th scope="col" class="hidden sm:table-cell w-[20%] px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="setSortField('targetSegment')">
									<div class="flex items-center">
										Target
										<span v-if="sortField === 'targetSegment'" class="ml-1">
											<svg v-if="sortOrder === 'asc'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
											</svg>
											<svg v-else class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
											</svg>
										</span>
									</div>
								</th>
								<!-- Date - fixed width -->
                                                                <th scope="col" class="hidden sm:table-cell w-[20%] px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="setSortField('scheduledDate')">
									<div class="flex items-center">
										Date
										<span v-if="sortField === 'scheduledDate'" class="ml-1">
											<svg v-if="sortOrder === 'asc'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
											</svg>
											<svg v-else class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
											</svg>
										</span>
									</div>
								</th>
								<!-- Created By - fixed width -->
								<th scope="col" class="hidden lg:table-cell w-[15%] px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="setSortField('createdByName')">
									<div class="flex items-center">
										Created By
										<span v-if="sortField === 'createdByName'" class="ml-1">
											<svg v-if="sortOrder === 'asc'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
											</svg>
											<svg v-else class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
											</svg>
										</span>
									</div>
								</th>
								<!-- Status - fixed width -->
                                                                <th scope="col" class="hidden sm:table-cell w-[15%] px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="setSortField('status')">
									<div class="flex items-center">
										Status
										<span v-if="sortField === 'status'" class="ml-1">
											<svg v-if="sortOrder === 'asc'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
											</svg>
											<svg v-else class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
											</svg>
										</span>
									</div>
								</th>
								<!-- Actions column -->
								<th scope="col" class="w-[10%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody class="bg-white divide-y divide-gray-200">
							<tr v-for="campaign in filteredCampaigns"
								:key="campaign.id"
								class="transition-colors duration-150 group relative"
								:class="{
									'cursor-pointer hover:bg-indigo-50': !campaign.archived && !isProcessingStatus(campaign.status),
									'cursor-pointer bg-gray-50 hover:bg-gray-100': campaign.archived && !isProcessingStatus(campaign.status),
									'cursor-not-allowed opacity-60 bg-gray-50': isProcessingStatus(campaign.status)
								}"
								@click="navigateToCampaign(campaign.id)">
								<!-- Checkbox cell -->
                                                                <td class="hidden sm:table-cell px-3 py-4 w-[5%]" @click.stop>
									<div class="flex items-center">
										<input
											type="checkbox"
											:checked="isSelected(campaign.id)"
											@change="toggleCampaignSelection(campaign.id)"
											class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
										>
									</div>
								</td>
                                                                <td class="px-3 py-4 w-auto sm:w-[18%]">
									<div class="w-full">
										<div class="flex items-center">
											<div class="flex items-center">
                                                                               <div v-clamp-fade class="text-sm font-medium group-hover:text-indigo-700 transition-colors duration-150 whitespace-normal break-words"
                                                                               :class="{'text-gray-900': !campaign.archived, 'text-gray-600': campaign.archived}">
                                                                               {{ campaign.name }}
												</div>
												<span v-if="campaign.archived" class="ml-2 inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
													<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
													</svg>
													Archived
												</span>
											</div>
											<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-150" viewBox="0 0 20 20" fill="currentColor">
												<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
											</svg>
										</div>
										<div class="text-sm text-gray-500 truncate">{{ campaign.planName }}</div>
									</div>
								</td>
                                                                <td class="hidden sm:table-cell px-2 py-4 w-[15%]">
									<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full truncate"
										:class="{
											'bg-green-100 text-green-800': campaign.type === 'Promotion',
											'bg-blue-100 text-blue-800': campaign.type === 'Education',
											'bg-orange-100 text-orange-800': campaign.type === 'Awareness',
											'bg-gray-100 text-gray-800': !campaign.type
										}">
										{{ campaign.type ? (campaign.type.charAt(0).toUpperCase() + campaign.type.slice(1).toLowerCase()) : 'Unknown' }}
									</span>
								</td>
                                                                <td class="hidden sm:table-cell px-2 py-4 w-[20%]">
									<div class="text-sm text-gray-500 truncate">{{ campaign.targetSegment || 'All Users' }}</div>
								</td>
                                                                <td class="hidden sm:table-cell px-2 py-4 w-[20%]">
									<div class="text-sm text-gray-500 truncate">{{ formatDate(campaign.scheduledDate) || 'Not scheduled' }}</div>
								</td>
                                                                <td class="hidden lg:table-cell px-2 py-4 w-[15%]">
									<div class="text-sm text-gray-500 truncate">{{ campaign.createdByName || 'Unknown' }}</div>
								</td>
                                                                <td class="hidden sm:table-cell px-2 py-4 w-[15%]">
									<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full text-white truncate"
										:style="getStatusStyle(campaign.status)">
										{{ getDisplayStatus(campaign.status) }}
									</span>
								</td>
								<!-- Actions column -->
								<td class="px-2 py-4 w-[10%] text-right text-sm font-medium">
									<div class="flex justify-end space-x-2">
										<button @click.stop="confirmDuplicateCampaign(campaign.id)" class="text-gray-500 hover:text-indigo-600 focus:outline-none">
											<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
												<path stroke-linecap="round" stroke-linejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
											</svg>
										</button>
										<button @click.stop="confirmDeleteCampaign(campaign.id)" class="text-red-600 hover:text-red-900 focus:outline-none">
											<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
											</svg>
										</button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Empty state -->
			<div v-else class="text-center py-12 bg-white rounded-lg shadow">
				<svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">No campaigns found</h3>
                                <p class="mt-1 text-sm text-gray-500">
                                        <template v-if="searchQuery || selectedType">
                                                Try adjusting your filters
                                        </template>
                                        <template v-else>
                                                Get started by creating a new campaign with
                                                <router-link to="/chat" class="text-purple-600 hover:underline">new chat</router-link>
                                                using Raleon's AI Strategst
                                        </template>
                                </p>
			</div>
		</div>

		<!-- Plans Tab Content -->
		<div v-if="showPlans" class="mt-4">
			<!-- Loading skeleton -->
			<div v-if="!plansLoaded" class="space-y-4">
				<div v-for="i in 3" :key="i" class="bg-white rounded-lg p-4 border border-gray-200 animate-pulse">
					<div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
					<div class="h-4 bg-gray-200 rounded w-1/2"></div>
				</div>
			</div>

			<!-- Plans list -->
			<div v-else-if="plansLoaded && plans.length > 0" class="space-y-4">
				<div v-for="plan in plans" :key="plan.id" class="w-full">
					<!-- Plan Card -->
					<div @click="navigateToPlan(plan.id)"
						class="p-2 mb-2 rounded-lg border hover:border-purple-200 hover:shadow-md transition-all duration-200 group relative overflow-hidden cursor-pointer"
						:class="{
							'bg-white border-gray-200': !plan.archived,
							'bg-gray-50 border-gray-300': plan.archived
						}">
						<!-- Archived diagonal banner if plan is archived -->
						<div v-if="plan.archived" class="absolute top-0 right-0 w-20 h-20 overflow-hidden pointer-events-none">
							<div class="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 rotate-45 bg-gray-500 text-white text-xs font-bold py-1 w-28 text-center shadow-md">
								ARCHIVED
							</div>
						</div>
						<!-- Header Section -->
						<div class="w-full text-left p-4 flex items-start gap-4">
							<div class="flex-1">
								<div class="flex items-center gap-2 text-sm mb-2">
									<span class="px-2.5 py-1 rounded-full bg-purple-50 text-purple-600 font-medium">
										{{ formatDate(plan.startdate) }} - {{ formatDate(plan.enddate) }}
									</span>
									<span class="px-2.5 py-1 rounded-full bg-gray-50 text-gray-600">
										{{ plan.businessGoal }}
									</span>
								</div>
								<div class="flex items-center mb-1">
									<h3 class="text-lg font-medium" :class="{'text-gray-900': !plan.archived, 'text-gray-600': plan.archived}">{{ plan.name }}</h3>
									<span v-if="plan.archived" class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
										<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
										</svg>
										Archived
									</span>
								</div>
								<p class="text-gray-600">{{ plan.description }}</p>
							</div>
							<div class="flex flex-col gap-2 text-sm text-gray-500">
								<div class="flex items-center gap-2">
									<svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
										<path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4-8 5-8-5V6l8 5 8-5v2z" />
									</svg>
									<span>{{ plan.campaignCount || 0 }} campaigns</span>
								</div>
								<div v-if="plan.createdByName" class="flex items-center gap-2">
									<svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
										<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
									</svg>
									<span>Created by {{ plan.createdByName }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Empty state -->
			<div v-else class="text-center py-12 bg-white rounded-lg shadow">
				<svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">No plans found</h3>
				<p class="mt-1 text-sm text-gray-500">
					Get started by creating a new plan
				</p>
			</div>
		</div>
	</div>

	<!-- Simple Toast component for notifications -->
	<Teleport to="body">
		<div v-if="toast.show" class="fixed bottom-5 right-5 z-50 min-w-[300px]">
			<div class="flex items-center p-4 mb-4 text-sm text-white rounded-lg shadow-md"
				:class="{
					'bg-emerald-500': toast.type === 'success',
					'bg-blue-500': toast.type === 'info',
					'bg-amber-500': toast.type === 'warning',
					'bg-rose-500': toast.type === 'error'
				}">
				<div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg">
					<svg v-if="toast.type === 'success'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>
					<svg v-else-if="toast.type === 'info'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
					</svg>
					<svg v-else-if="toast.type === 'warning'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
					</svg>
					<svg v-else-if="toast.type === 'error'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
					</svg>
				</div>
				<div class="ml-3 font-medium">{{ toast.message }}</div>
				<button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex h-8 w-8 text-white hover:bg-opacity-25" @click="closeToast">
					<span class="sr-only">Close</span>
					<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
					</svg>
				</button>
			</div>
		</div>
	</Teleport>

	<!-- Single Delete Confirmation Modal -->
	<div v-if="showDeleteConfirmation" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
		<div class="relative mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
			<div class="mt-3 text-center">
				<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
					<svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
					</svg>
				</div>
				<h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Delete Campaign</h3>
				<div class="mt-2 px-7 py-3">
					<p class="text-sm text-gray-500">Are you sure you want to delete this campaign? This action cannot be undone.</p>
				</div>
				<div class="flex justify-center gap-4 mt-3">
					<button @click="showDeleteConfirmation = false" class="px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300">
						Cancel
					</button>
					<button @click="deleteCampaign" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
						Delete
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Duplicate Confirmation Modal -->
	<div v-if="showDuplicateConfirmation" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
		<div class="relative mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
			<div class="mt-3 text-center">
				<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100">
					<svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
						<path stroke-linecap="round" stroke-linejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
					</svg>
				</div>
				<h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Duplicate Campaign</h3>
				<div class="mt-2 px-7 py-3">
					<p class="text-sm text-gray-500">Are you sure you want to duplicate this campaign? This will create a new campaign with the same tasks and steps.</p>
				</div>
				<div class="flex justify-center gap-4 mt-3">
					<button @click="showDuplicateConfirmation = false" class="px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300">
						Cancel
					</button>
					<button @click="duplicateCampaign" class="px-4 py-2 bg-indigo-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
						Duplicate
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Bulk Delete Confirmation Modal -->
	<div v-if="showBulkDeleteConfirmation" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
		<div class="relative mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
			<div class="mt-3 text-center">
				<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
					<svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
					</svg>
				</div>
				<h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Bulk Delete Campaigns</h3>
				<div class="mt-2 px-7 py-3">
					<p class="text-sm text-gray-500">Are you sure you want to delete {{ selectedCampaigns.length }} {{ selectedCampaigns.length === 1 ? 'campaign' : 'campaigns' }}? This action cannot be undone.</p>
				</div>
				<div class="flex justify-center gap-4 mt-3">
					<button @click="showBulkDeleteConfirmation = false" class="px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300">
						Cancel
					</button>
					<button @click="bulkDeleteCampaigns" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
						Delete All
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Bulk Status Update Confirmation Modal -->
	<div v-if="showBulkStatusConfirmation" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
		<div class="relative mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
			<div class="mt-3 text-center">
				<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100">
					<svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
					</svg>
				</div>
				<h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Update Campaign Status</h3>
				<div class="mt-2 px-7 py-3">
					<p class="text-sm text-gray-500">
						Are you sure you want to update {{ selectedCampaigns.length }} {{ selectedCampaigns.length === 1 ? 'campaign' : 'campaigns' }} to status:
					</p>
					<div class="mt-2 inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium text-white" :style="getStatusStyle(bulkStatusToApply)">
						{{ getDisplayStatus(bulkStatusToApply) }}
					</div>
				</div>
				<div class="flex justify-center gap-4 mt-3">
					<button @click="showBulkStatusConfirmation = false" class="px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300">
						Cancel
					</button>
					<button @click="bulkUpdateCampaignStatus" class="px-4 py-2 bg-indigo-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
						Update Status
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import { isFeatureAvailable } from '../services/features.js';
import { customerIOTrackEvent } from '../services/customerio.js';
import { nextTick } from 'vue';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'SignalLibrary',
	data() {
		return {
			campaignsLoaded: false, // Track if campaigns have been loaded
			plansLoaded: false, // Track if plans have been loaded
			pollingInterval: null, // For periodic status updates
			showCampaigns: true, // Default to Campaigns tab
			showPlans: false,
			campaigns: [],
			plans: [],
			searchQuery: '', // For filtering by plan name
			selectedType: '', // For filtering by campaign type
			selectedStatus: '', // For filtering by status
			selectedPlan: '', // For filtering by specific plan
			showArchived: false, // Whether to show archived plans and campaigns
			showStatusDropdown: false, // Control visibility of custom status dropdown
			showTypeDropdown: false, // Control visibility of custom type dropdown
			showFilterDropdown: false, // Control visibility of unified filter dropdown
			selectedCampaigns: [], // For bulk delete functionality
			showDeleteConfirmation: false, // For delete confirmation modal
			campaignToDelete: null, // For single campaign delete confirmation
			showDuplicateConfirmation: false, // For duplicate confirmation modal
			campaignToDuplicate: null, // For single campaign duplicate confirmation
			showBulkDeleteConfirmation: false, // For bulk delete confirmation modal
			showBulkStatusDropdown: false, // For bulk status update dropdown
			showBulkStatusConfirmation: false, // For bulk status update confirmation modal
			bulkStatusToApply: null, // Status to apply in bulk
			sortOrder: 'asc', // Default sort order for date
			sortField: 'scheduledDate', // Default field to sort by
			dateFilter: {
				startDate: '', // For filtering by start date
				endDate: '' // For filtering by end date
			},
			statusColors: {
				'Campaign Ready': '#6366F1', // Indigo
				'Ready for Copywriting': '#8B5CF6', // Purple
				'In Copywriting': '#A78BFA', // Light purple
				'Ready for Design': '#EC4899', // Pink
				'In Design': '#F472B6', // Light pink
				'Quality Check': '#F59E0B', // Amber
				'Ready for Review': '#10B981', // Emerald
				'In Review': '#34D399', // Green
				'Approved': '#3B82F6', // Blue
                                'Complete': '#059669', // Green
                                'Archive': '#6B7280'
                        },
                        statusOptions: [
				{ label: 'Campaign Ready', value: 'Campaign Ready', color: '#6366F1' }, // Indigo
				{ label: 'Ready for Copywriting', value: 'Ready for Copywriting', color: '#8B5CF6' }, // Purple
				{ label: 'In Copywriting', value: 'In Copywriting', color: '#A78BFA' }, // Light purple
				{ label: 'Ready for Design', value: 'Ready for Design', color: '#EC4899' }, // Pink
				{ label: 'In Design', value: 'In Design', color: '#F472B6' }, // Light pink
				{ label: 'Quality Check', value: 'Quality Check', color: '#F59E0B' }, // Amber
				{ label: 'Ready for Review', value: 'Ready for Review', color: '#10B981' }, // Emerald
				{ label: 'In Review', value: 'In Review', color: '#34D399' }, // Green
                                { label: 'Approved', value: 'Approved', color: '#3B82F6' }, // Blue
                                { label: 'Done', value: 'Complete', color: '#059669' }, // Green
                                { label: 'Archive', value: 'Archive', color: '#6B7280' } // Slate
                        ],
			toast: {
				show: false,
				message: '',
				type: 'info', // 'success', 'info', 'warning', 'error'
				timeout: null
			}
		}
	},
	computed: {
		campaignsCount() {
			return this.campaigns.length;
		},
		plansCount() {
			return this.plans.length;
		},
		isFeatureAvailable() {
			return isFeatureAvailable('ai-strategist');
		},
		// Check if there are campaigns currently processing
		hasProcessingCampaigns() {
			return this.campaigns.some(campaign => this.isProcessingStatus(campaign.status));
		},
		activeFilterCount() {
			let count = 0;
			if (this.selectedType) count++;
			if (this.selectedStatus) count++;
			if (this.selectedPlan) count++;
			if (this.dateFilter.startDate) count++;
			if (this.dateFilter.endDate) count++;
			return count;
		},
		availablePlans() {
			// Get unique plans from campaigns that have associated plan data
			const planMap = new Map();
			this.campaigns.forEach(campaign => {
				if (campaign.planName && campaign.versionId) {
					planMap.set(campaign.versionId, {
						versionId: campaign.versionId,
						name: campaign.planName
					});
				}
			});
			return Array.from(planMap.values()).sort((a, b) => a.name.localeCompare(b.name));
		},
                hasActiveFilters() {
                        return this.activeFilterCount > 0;
                },
                isAllSelected() {
                        return this.filteredCampaigns.length > 0 && this.selectedCampaigns.length === this.filteredCampaigns.length;
                },
                filteredCampaigns() {
			let result = [...this.campaigns];

			// Filter out campaigns with Planning status or null/undefined status
			result = result.filter(campaign =>
				campaign.status &&
				campaign.status !== 'Planning' &&
				campaign.status !== 'Not Started'
			);

                        // If showArchived is false, filter out archived campaigns and Archive status
                        if (!this.showArchived) {
                                result = result.filter(campaign => !campaign.archived && campaign.status !== 'Archive');
                        }

			// Apply search filter if query exists
			if (this.searchQuery) {
				const query = this.searchQuery.toLowerCase();
				result = result.filter(campaign =>
					(campaign.name && campaign.name.toLowerCase().includes(query)) ||
					(campaign.planName && campaign.planName.toLowerCase().includes(query))
				);
			}

			// Filter by campaign type if selected
			if (this.selectedType) {
				result = result.filter(campaign => campaign.type === this.selectedType);
			}

			// Filter by status if selected
			if (this.selectedStatus) {
				// Handle special case for "Complete" which is displayed as "Done"
				if (this.selectedStatus === 'Complete') {
					result = result.filter(campaign => campaign.status === 'Complete');
				} else {
					result = result.filter(campaign => campaign.status === this.selectedStatus);
				}
			}

			// Filter by date range if selected
			if (this.dateFilter.startDate || this.dateFilter.endDate) {
				result = result.filter(campaign => {
					if (!campaign.scheduledDate) return false;

					const campaignDate = new Date(campaign.scheduledDate);
					// Reset time to midnight for accurate date comparison
					campaignDate.setHours(0, 0, 0, 0);

					// Check start date if provided
					if (this.dateFilter.startDate) {
						const startDate = new Date(this.dateFilter.startDate);
						startDate.setHours(0, 0, 0, 0);
						if (campaignDate < startDate) return false;
					}

					// Check end date if provided
					if (this.dateFilter.endDate) {
						const endDate = new Date(this.dateFilter.endDate);
						endDate.setHours(23, 59, 59, 999); // End of day
						if (campaignDate > endDate) return false;
					}

					return true;
				});
			}

			// Filter by plan if selected in dropdown or query parameter is present
			const planFilter = this.selectedPlan || this.$route.query.plan;
			if (planFilter) {
				result = result.filter(campaign => campaign.versionId == planFilter);
			}

			// Sort by the selected field
			result.sort((a, b) => {
				// Special handling for date fields
				if (this.sortField === 'scheduledDate') {
					const dateA = new Date(a.scheduledDate || 0);
					const dateB = new Date(b.scheduledDate || 0);
					return this.sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
				}

				// Handle string fields
				const valueA = a[this.sortField] || '';
				const valueB = b[this.sortField] || '';

				// Case insensitive string comparison
				if (typeof valueA === 'string' && typeof valueB === 'string') {
					const comparison = valueA.toLowerCase().localeCompare(valueB.toLowerCase());
					return this.sortOrder === 'asc' ? comparison : -comparison;
				}

				// Fallback for other types
				if (valueA < valueB) return this.sortOrder === 'asc' ? -1 : 1;
				if (valueA > valueB) return this.sortOrder === 'asc' ? 1 : -1;
				return 0;
			});

                        return result;
                },
        },
        directives: {
                clampFade: {
                        mounted(el) {
                                const applyClamp = () => {
                                        el.classList.remove('line-clamp-2', 'line-clamp-2-fade');
                                        const lineHeight = parseFloat(getComputedStyle(el).lineHeight || '16');
                                        const lines = el.scrollHeight / lineHeight;
                                        if (lines > 2) {
                                                el.classList.add('line-clamp-2', 'line-clamp-2-fade');
                                        }
                                };
                                el.__clampFadeHandler = applyClamp;
                                nextTick(applyClamp);
                                window.addEventListener('resize', applyClamp);
                        },
                        updated(el) {
                                if (el.__clampFadeHandler) el.__clampFadeHandler();
                        },
                        unmounted(el) {
                                window.removeEventListener('resize', el.__clampFadeHandler);
                        }
                }
        },
        methods: {
		// Fetch campaigns from the API
		async fetchCampaigns() {
			this.campaignsLoaded = false;
			try {
				// Build filter to exclude campaigns from archived plans unless showArchived is true
				const filter = {
					includeArchived: this.showArchived
				};

				const queryParams = new URLSearchParams();
				queryParams.append('filter', JSON.stringify(filter));

				const response = await fetch(`${URL_DOMAIN}/planner/campaigns?${queryParams.toString()}`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const result = await response.json();
					this.campaigns = result.data.map(campaign => ({
							id: campaign.id,
							name: campaign.name,
							type: campaign.type,
							taskId: campaign.task ? campaign.task.id : null,
							status: campaign.task ? campaign.task.status : 'Not Started',
							planId: campaign.planId,
							planName: campaign.planName,
							versionId: campaign.versionId,
							description: campaign.description,
							scheduledDate: campaign.scheduledDate,
							targetSegment: campaign.targetSegment,
							// Store creator information
							createdBy: campaign.createdBy,
							createdByName: campaign.createdByName,
							// Store archived status if available
							archived: campaign.archived || false
						}));
				} else {
					console.error("Failed to fetch campaigns:", response.statusText);
					this.showToast('Error fetching campaigns', 'error');
				}
			} catch (error) {
				console.error("Error fetching campaigns:", error);
				this.showToast('Error fetching campaigns', 'error');
			} finally {
				this.campaignsLoaded = true;
			}
		},

		// Fetch plans from the API
		async fetchPlans() {
			this.plansLoaded = false;
			try {
				// Create a filter to exclude plans with hiddenToUsers=true or isDeleted=true
				// Only filter out archived plans if showArchived is false
				const whereConditions = [
					{
						or: [
							{ hiddenToUsers: false },
							{ hiddenToUsers: { eq: null } }
						]
					},
					{
						or: [
							{ isDeleted: false },
							{ isDeleted: { eq: null } }
						]
					}
				];

				// Only add the archived condition if we don't want to show archived plans
				if (!this.showArchived) {
					whereConditions.push({
						or: [
							{ archived: false },
							{ archived: { eq: null } }
						]
					});
				}

				const filter = {
					where: {
						and: whereConditions
					}
				};

				const response = await fetch(`${URL_DOMAIN}/organizations/organization-planner-plans?filter=${encodeURIComponent(JSON.stringify(filter))}`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const data = await response.json();

					// Process plans to add campaignCount
						this.plans = data.map(plan => {
						// Calculate campaign count using the getTotalTasks method
						const campaignCount = this.calculateCampaignCount(plan);
						return {
							...plan,
							campaignCount
						};
					});
				} else {
					console.error("Failed to fetch plans:", response.statusText);
					this.showToast('Error fetching plans', 'error');
				}
			} catch (error) {
				console.error("Error fetching plans:", error);
				this.showToast('Error fetching plans', 'error');
			} finally {
				this.plansLoaded = true;
			}
		},

		// Calculate campaign count for a plan
		calculateCampaignCount(plan) {
			// Check if plan has plannerPlanVersions
			if (!plan.plannerPlanVersions || !plan.plannerPlanVersions.length) {
				return 0;
			}

			// Get the first plan version
			const planVersion = plan.plannerPlanVersions[0];

			// Check if planVersion has plannerCampaigns
			if (!planVersion.plannerCampaigns) {
				return 0;
			}

			// Return the length of plannerCampaigns array
			return planVersion.plannerCampaigns.length;
		},

		// Toggle sort order between ascending and descending
		toggleSortOrder() {
			this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
		},

		// Set the field to sort by
		setSortField(field) {
			// If clicking the same field, toggle the sort order
			if (this.sortField === field) {
				this.toggleSortOrder();
			} else {
				// If clicking a new field, set it as the sort field and reset to default sort order (asc)
				this.sortField = field;
				this.sortOrder = 'asc';
			}
		},

		// Clear date filter
		clearDateFilter() {
			this.dateFilter.startDate = '';
			this.dateFilter.endDate = '';
		},

		// Clear all filters
		clearAllFilters() {
			this.selectedType = '';
			this.selectedStatus = '';
			this.selectedPlan = '';
			this.dateFilter.startDate = '';
			this.dateFilter.endDate = '';
			this.showArchived = false;
		},

		// Toggle filter dropdown
		toggleFilterDropdown() {
			this.showFilterDropdown = !this.showFilterDropdown;

			// Close other dropdowns if open
			if (this.showFilterDropdown) {
				this.showStatusDropdown = false;
				this.showTypeDropdown = false;

				// Add event listener to close dropdown when clicking outside
				setTimeout(() => {
					document.addEventListener('click', this.closeFilterDropdown);
				}, 0);
			}
		},

		// Close filter dropdown when clicking outside
		closeFilterDropdown(event) {
			const dropdown = document.querySelector('.relative button + .absolute');
			if (dropdown && !dropdown.contains(event.target) && !dropdown.previousElementSibling.contains(event.target)) {
				this.showFilterDropdown = false;
				document.removeEventListener('click', this.closeFilterDropdown);
			}
		},

		// Toggle status dropdown visibility
		toggleStatusDropdown() {
			// Close type dropdown if open
			if (this.showTypeDropdown) {
				this.showTypeDropdown = false;
				document.removeEventListener('click', this.closeTypeDropdown);
			}

			this.showStatusDropdown = !this.showStatusDropdown;

			// Add event listener to close dropdown when clicking outside
			if (this.showStatusDropdown) {
				setTimeout(() => {
					document.addEventListener('click', this.closeStatusDropdown);
				}, 0);
			}
		},

		// Close status dropdown
		closeStatusDropdown(event) {
			// Check if click is outside the dropdown
			const dropdown = document.querySelector('.w-56.relative');
			if (dropdown && !dropdown.contains(event.target)) {
				this.showStatusDropdown = false;
				document.removeEventListener('click', this.closeStatusDropdown);
			}
		},

		// Toggle type dropdown visibility
		toggleTypeDropdown() {
			// Close status dropdown if open
			if (this.showStatusDropdown) {
				this.showStatusDropdown = false;
				document.removeEventListener('click', this.closeStatusDropdown);
			}

			this.showTypeDropdown = !this.showTypeDropdown;

			// Add event listener to close dropdown when clicking outside
			if (this.showTypeDropdown) {
				setTimeout(() => {
					document.addEventListener('click', this.closeTypeDropdown);
				}, 0);
			}
		},

		// Close type dropdown
		closeTypeDropdown(event) {
			// Check if click is outside the dropdown
			const dropdown = document.querySelector('.w-48.relative');
			if (dropdown && !dropdown.contains(event.target)) {
				this.showTypeDropdown = false;
				document.removeEventListener('click', this.closeTypeDropdown);
			}
		},

		// Select type and close dropdown
		selectType(type) {
			this.selectedType = type;
			this.showTypeDropdown = false;
			document.removeEventListener('click', this.closeTypeDropdown);
		},

		// Select status and close dropdown
		selectStatus(status) {
			this.selectedStatus = status;
			this.showStatusDropdown = false;
			document.removeEventListener('click', this.closeStatusDropdown);
		},

		// Get color for a status
		getStatusColor(status) {
			// Find the status in statusOptions
			const statusOption = this.statusOptions.find(option => option.value === status);
			if (statusOption) {
				return statusOption.color;
			}

			// If not found in statusOptions, check statusColors
			if (this.statusColors[status]) {
				return this.statusColors[status];
			}

			// Default color if not found
			return null;
		},

		// Get display label for a status
		getStatusLabel(status) {
			// Find the status in statusOptions
			const statusOption = this.statusOptions.find(option => option.value === status);
			if (statusOption) {
				return statusOption.label;
			}

			// If not found in statusOptions, use getDisplayStatus
			return this.getDisplayStatus(status);
		},

		// Format date for display
		formatDate(dateString) {
			if (!dateString) return '';

			// The direct string parsing approach prevents timezone issues
			if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
				// Extract year, month, day directly from string
				const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));

				// Map month number to month name
				const monthNames = [
					"January", "February", "March", "April", "May", "June",
					"July", "August", "September", "October", "November", "December"
				];

				// Format directly without using Date object
				return `${monthNames[month-1]} ${day}, ${year}`;
			}

			// Fallback to UTC-based Date object for other date formats
			try {
				// Create a UTC date to avoid timezone shifts
				const date = new Date(dateString);
				// Force UTC timezone display
				return date.toLocaleDateString('en-US', {
					month: 'long',
					day: 'numeric',
					year: 'numeric',
					timeZone: 'UTC' // Force UTC timezone
				});
			} catch (error) {
				console.error('Error formatting date:', error, dateString);
				return dateString; // Return original string as fallback
			}
		},

		// Map status values to display text
		getDisplayStatus(status) {
			if (!status) return 'Unknown';

			const statusMap = {
				// Standard campaign statuses
				'Campaign Ready': 'Campaign Ready',
				'Ready for Copywriting': 'Ready for Copywriting',
				'In Copywriting': 'In Copywriting',
				'Ready for Design': 'Ready for Design',
				'In Design': 'In Design',
				'Quality Check': 'Quality Check',
				'Ready for Review': 'Ready for Review',
				'In Review': 'In Review',
				'Approved': 'Approved',
                                'Complete': 'Done',
                                'Done': 'Done',
                                'Archive': 'Archive',

				// Legacy or alternative statuses
				'Ready': 'Ready',
				'In Progress': 'In Progress',
                                'Not Started': 'Not Started',
                                'Processing': 'Generating',
                                'Planning': 'Planning'
                        };

			return statusMap[status] || status;
		},

		// Get style for status badge
		getStatusStyle(status) {
			if (!status) return { backgroundColor: '#9CA3AF' }; // Default gray

			// Check if we have a specific color for this status
			if (this.statusColors[status]) {
				return { backgroundColor: this.statusColors[status] };
			}

			// Fallback colors for statuses not in our map
			const fallbackColors = {
				'Ready': '#8B5CF6', // Purple (same as Ready for Copywriting)
				'In Progress': '#A78BFA', // Light purple (same as In Copywriting)
				'Processing': '#A78BFA', // Light purple (same as In Copywriting)
				'Not Started': '#9CA3AF', // Gray
                                'Complete': '#059669', // Green (same as Done)
                                'Done': '#059669', // Green
                                'Archive': '#6B7280'
                        };

			return {
				backgroundColor: fallbackColors[status] || '#9CA3AF' // Default to gray if no match
			};
		},

		// Handle changes to the archived toggle
		onArchivedToggleChange() {
			// Refresh campaigns and plans with the new filter state
			this.fetchCampaigns();
			this.fetchPlans();
		},

		// Check if a status indicates processing/generation in progress
		isProcessingStatus(status) {
			// Define statuses that indicate processing is ongoing
			const processingStatuses = ['Planning', 'Not Started', 'In Progress', 'Generating', 'Processing'];
			return processingStatuses.includes(status) || !status;
		},

		// Navigate to campaign details (via task)
		navigateToCampaign(campaignId) {
			// Find the task associated with this campaign
			const campaign = this.campaigns.find(c => c.id === campaignId);
			if (campaign) {
				console.log(`Clicked campaign ${campaignId}, status: "${campaign.status}", isProcessing: ${this.isProcessingStatus(campaign.status)}`);
				// Check if campaign is still processing
				if (this.isProcessingStatus(campaign.status)) {
					this.showToast('Campaign is still being generated. Please wait until it\'s ready.', 'info');
					return;
				}
				// Navigate to the task page
				console.log('Navigating to campaign:', campaign);
				this.$router.push({
					path: `/ai-strategist/tasks/${campaign.taskId}`,
					query: { fromPlanning: 'true' }
				});
			}
		},

		// Navigate to plan details
		navigateToPlan(planId) {
			this.$router.push(`/ai-strategist/planning/plan/${planId}`);
		},

		// Update only the status of processing campaigns using the same endpoint as fetchCampaigns
		async updateProcessingCampaignStatuses() {
			const processingCampaigns = this.campaigns.filter(campaign => this.isProcessingStatus(campaign.status));

			console.log('Processing campaigns to check:', processingCampaigns.map(c => ({ id: c.id, name: c.name, currentStatus: c.status })));

			if (processingCampaigns.length === 0) return;

			try {
				// Use the same endpoint as fetchCampaigns but filter for processing campaigns only
				const processingIds = processingCampaigns.map(c => c.id);
				const filter = {
					includeArchived: this.showArchived,
					campaignIds: processingIds // Add campaign IDs filter if supported
				};

				const queryParams = new URLSearchParams();
				queryParams.append('filter', JSON.stringify(filter));

				const response = await fetch(`${URL_DOMAIN}/planner/campaigns?${queryParams.toString()}`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const result = await response.json();
					console.log('Status update API response:', result);

					// Update statuses for the campaigns we received
					result.data.forEach(updatedCampaign => {
						const campaignIndex = this.campaigns.findIndex(c => c.id === updatedCampaign.id);
						if (campaignIndex !== -1) {
							const oldStatus = this.campaigns[campaignIndex].status;
							const newStatus = updatedCampaign.task ? updatedCampaign.task.status : 'Not Started';
							if (oldStatus !== newStatus) {
								console.log(`Campaign ${updatedCampaign.id} status update: ${oldStatus} -> ${newStatus}`);
								this.campaigns[campaignIndex].status = newStatus;
							}
						}
					});
				} else {
					console.error('Failed to fetch campaign statuses:', response.statusText);
				}
			} catch (error) {
				console.error('Error updating campaign statuses:', error);
			}
		},

		// Start polling for campaign status updates
		startStatusPolling() {
			// Only start polling if there are processing campaigns
			if (this.hasProcessingCampaigns) {
				this.pollingInterval = setInterval(() => {
					// Only update statuses if we still have processing ones
					if (this.hasProcessingCampaigns) {
						this.updateProcessingCampaignStatuses();
					} else {
						// Stop polling if no more processing campaigns
						this.stopStatusPolling();
					}
				}, 5000); // Poll every 5 seconds
			}
		},

		// Stop polling for campaign status updates
		stopStatusPolling() {
			if (this.pollingInterval) {
				clearInterval(this.pollingInterval);
				this.pollingInterval = null;
			}
		},

		// Show toast notification
		showToast(message, type = 'info') {
			// Clear any existing timeout
			if (this.toast.timeout) {
				clearTimeout(this.toast.timeout);
			}

			// Set toast properties
			this.toast.message = message;
			this.toast.type = type;
			this.toast.show = true;

			// Auto-hide after 5 seconds
			this.toast.timeout = setTimeout(() => {
				this.closeToast();
			}, 5000);
		},

		// Close toast notification
		closeToast() {
			this.toast.show = false;
		},
		navigateToTasksForPlan(planId) {
			this.$router.push(`/ai-strategist/planning/plan/${planId}`);
		},
		// Toast methods
		showToast(message, type = 'info', duration = 5000) {
			// Clear any existing toast timeout
			if (this.toast.timeout) {
				clearTimeout(this.toast.timeout);
			}

			// Set the toast properties
			this.toast.message = message;
			this.toast.type = type;
			this.toast.show = true;

			// Auto-hide the toast after specified duration
			if (duration > 0) {
				this.toast.timeout = setTimeout(() => {
					this.closeToast();
				}, duration);
			}
		},

		closeToast() {
			this.toast.show = false;
			if (this.toast.timeout) {
				clearTimeout(this.toast.timeout);
				this.toast.timeout = null;
			}
		},

		// Campaign selection methods for bulk delete
		isSelected(campaignId) {
			return this.selectedCampaigns.includes(campaignId);
		},

		toggleCampaignSelection(campaignId) {
			const index = this.selectedCampaigns.indexOf(campaignId);
			if (index === -1) {
				this.selectedCampaigns.push(campaignId);
			} else {
				this.selectedCampaigns.splice(index, 1);
			}
		},

		toggleSelectAll() {
			if (this.isAllSelected) {
				// Deselect all
				this.selectedCampaigns = [];
			} else {
				// Select all
				this.selectedCampaigns = this.filteredCampaigns.map(campaign => campaign.id);
			}
		},

		// Delete a single campaign
		confirmDeleteCampaign(campaignId) {
			this.campaignToDelete = campaignId;
			this.showDeleteConfirmation = true;
		},

		async deleteCampaign() {
			if (!this.campaignToDelete) return;

			try {
				const response = await fetch(`${URL_DOMAIN}/planner/campaign/${this.campaignToDelete}`, {
					method: 'DELETE',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const result = await response.json();
					if (result.status === 200) {
						// Remove the campaign from the list
						this.campaigns = this.campaigns.filter(campaign => campaign.id !== this.campaignToDelete);
						this.showToast('Campaign deleted successfully', 'success');
					} else {
						this.showToast(result.message || 'Failed to delete campaign', 'error');
					}
				} else {
					this.showToast('Failed to delete campaign', 'error');
				}
			} catch (error) {
				console.error('Error deleting campaign:', error);
				this.showToast('Error deleting campaign: ' + error.message, 'error');
			}

			// Reset state
			this.showDeleteConfirmation = false;
			this.campaignToDelete = null;
		},

		// Duplicate a single campaign
		confirmDuplicateCampaign(campaignId) {
			this.campaignToDuplicate = campaignId;
			this.showDuplicateConfirmation = true;
		},

		async duplicateCampaign() {
			if (!this.campaignToDuplicate) return;

			try {
				const response = await fetch(`${URL_DOMAIN}/planner/campaigns/${this.campaignToDuplicate}/duplicate`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					await this.fetchCampaigns();
					this.showToast('Campaign duplicated successfully', 'success');
				} else {
					const error = await response.json();
					this.showToast(error.message || 'Failed to duplicate campaign', 'error');
				}
			} catch (error) {
				console.error('Error duplicating campaign:', error);
				this.showToast('Error duplicating campaign: ' + error.message, 'error');
			}

			// Reset state
			this.showDuplicateConfirmation = false;
			this.campaignToDuplicate = null;
		},

		// Bulk delete campaigns
		confirmBulkDelete() {
			if (this.selectedCampaigns.length === 0) return;
			this.showBulkDeleteConfirmation = true;
		},

		async bulkDeleteCampaigns() {
			if (this.selectedCampaigns.length === 0) return;

			try {
				const response = await fetch(`${URL_DOMAIN}/planner/campaigns/bulk-delete`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						campaignIds: this.selectedCampaigns
					})
				});

				if (response.ok) {
					const result = await response.json();
					if (result.status === 200) {
						// Remove the deleted campaigns from the list
						this.campaigns = this.campaigns.filter(campaign => !this.selectedCampaigns.includes(campaign.id));
						this.showToast(`Successfully deleted ${result.details.success} campaigns`, 'success');

						// If there were any failures, show them
						if (result.details.failed > 0) {
							console.error('Some campaigns failed to delete:', result.details.errors);
							this.showToast(`${result.details.failed} campaigns could not be deleted`, 'warning');
						}
					} else {
						this.showToast(result.message || 'Failed to delete campaigns', 'error');
					}
				} else {
					this.showToast('Failed to delete campaigns', 'error');
				}
			} catch (error) {
				console.error('Error deleting campaigns:', error);
				this.showToast('Error deleting campaigns: ' + error.message, 'error');
			}

			// Reset state
			this.showBulkDeleteConfirmation = false;
			this.selectedCampaigns = [];
		},

		// Toggle bulk status dropdown
		toggleBulkStatusDropdown() {
			this.showBulkStatusDropdown = !this.showBulkStatusDropdown;
			// Close other dropdowns
			if (this.showBulkStatusDropdown) {
				this.showStatusDropdown = false;
				this.showTypeDropdown = false;
				this.showFilterDropdown = false;
			}

			// Add click outside listener
			if (this.showBulkStatusDropdown) {
				setTimeout(() => {
					window.addEventListener('click', this.closeBulkStatusDropdown);
				}, 0);
			}
		},

		// Close bulk status dropdown when clicking outside
		closeBulkStatusDropdown(event) {
			const dropdown = document.querySelector('.origin-top-right');
			if (dropdown && !dropdown.contains(event.target) && !event.target.closest('button[data-dropdown="bulk-status"]')) {
				this.showBulkStatusDropdown = false;
				window.removeEventListener('click', this.closeBulkStatusDropdown);
			}
		},

		// Confirm bulk status update
		confirmBulkStatusUpdate(status) {
			this.bulkStatusToApply = status;
			this.showBulkStatusDropdown = false;
			this.showBulkStatusConfirmation = true;
		},

		// Bulk update campaign status
		async bulkUpdateCampaignStatus() {
			if (this.selectedCampaigns.length === 0 || !this.bulkStatusToApply) return;

			try {
				// Create an array of promises for each campaign update
				const updatePromises = this.selectedCampaigns.map(campaignId => {
					return fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}/status`, {
						method: 'PATCH',
						headers: {
							'Authorization': `Bearer ${localStorage.getItem('token')}`,
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({
							status: this.bulkStatusToApply
						})
					});
				});

				// Wait for all updates to complete
				const results = await Promise.allSettled(updatePromises);

				// Count successes and failures
				const successful = results.filter(result => result.status === 'fulfilled' && result.value.ok).length;
				const failed = results.length - successful;

				// Update the local campaign data
				this.campaigns = this.campaigns.map(campaign => {
					if (this.selectedCampaigns.includes(campaign.id)) {
						return {
							...campaign,
							status: this.bulkStatusToApply
						};
					}
					return campaign;
				});

				// Show success message
				this.showToast(`Successfully updated ${successful} campaign${successful !== 1 ? 's' : ''}`, 'success');

				// If there were any failures, show a warning
				if (failed > 0) {
					this.showToast(`${failed} campaign${failed !== 1 ? 's' : ''} could not be updated`, 'warning');
				}
			} catch (error) {
				console.error('Error updating campaign status:', error);
				this.showToast('Error updating campaign status: ' + error.message, 'error');
			}

			// Reset state
			this.showBulkStatusConfirmation = false;
			this.bulkStatusToApply = null;
			this.selectedCampaigns = [];
		},

		// Define individual toast methods instead of an object
		toastSuccess(message, options = {}) {
			this.showToast(message, 'success', options.timeout || 5000);
		},

		toastInfo(message, options = {}) {
			this.showToast(message, 'info', options.timeout || 5000);
		},

		toastWarning(message, options = {}) {
			this.showToast(message, 'warning', options.timeout || 5000);
		},

		toastError(message, options = {}) {
			this.showToast(message, 'error', options.timeout || 5000);
		},

		// Handle clicks outside of dropdowns
		handleOutsideClick(event) {
			// Handle filter dropdown
			if (this.showFilterDropdown) {
				const filterDropdown = document.querySelector('.filter-dropdown');
				const filterButton = document.querySelector('.filter-button');
				if (filterDropdown && filterButton &&
					!filterDropdown.contains(event.target) &&
					!filterButton.contains(event.target)) {
					this.showFilterDropdown = false;
				}
			}
		},
	},
	async mounted() {
		customerIOTrackEvent('Viewed AI Marketing Strategist');

		// Fetch both campaigns and plans
		await Promise.all([
			this.fetchCampaigns(),
			this.fetchPlans()
		]);

		// Add event listener to close dropdowns when clicking outside
		document.addEventListener('click', this.handleOutsideClick);

		// Start polling for campaign status updates if needed
		this.startStatusPolling();
	},
	unmounted() {
		// Clean up event listeners
		document.removeEventListener('click', this.closeStatusDropdown);
		document.removeEventListener('click', this.closeTypeDropdown);
		document.removeEventListener('click', this.closeFilterDropdown);
		document.removeEventListener('click', this.handleOutsideClick);

		// Stop polling
		this.stopStatusPolling();
	}
}
</script>

<style scoped>
.collapsed-box {
  max-height: 60px; /* Height of collapsed state */
  padding: 0.75rem;
}

.expanded-box {
  max-height: 800px; /* Large enough to fit expanded content */
  padding: 1.5rem;
}

@keyframes pulse {
	0%, 100% {
		opacity: 1;
	}
	50% {
		opacity: .5;
	}
}

.animate-pulse {
	animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-boxfadeIn {
	animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-pulse {
	animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.generating-card {
	position: relative;
}

.generating-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 200%;
	height: 100%;
	background: linear-gradient(90deg, rgba(167, 139, 250, 0), rgba(167, 139, 250, 0.1), rgba(167, 139, 250, 0));
	animation: shine 2s infinite linear;
	z-index: 1;
}

@keyframes shine {
        0% {
                left: -100%;
        }
        100% {
                left: 100%;
        }
}

.line-clamp-2 {
        display: -webkit-box;
        display: flex;
        -webkit-box-orient: vertical;
        flex-direction: column;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
}

.line-clamp-2-fade {
        position: relative;
}

.line-clamp-2-fade::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 2.5em;
        height: 1.2em;
        pointer-events: none;
        background: linear-gradient(to right, rgba(255,255,255,0), white 80%);
}
</style>

