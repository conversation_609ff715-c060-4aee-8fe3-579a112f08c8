import mitt, { Emitter } from 'mitt';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './BriefToolHandler';
import { EmailToolHandler } from './EmailToolHandler';
import { PlanToolHandler } from './PlanToolHandler';
import { AnalyticsToolHandler } from './AnalyticsToolHandler';
import { ImageToolHandler } from './ImageToolHandler';
import { ImageEditToolHandler } from './ImageEditToolHandler';
import { ImageTextEditToolHandler } from './ImageTextEditToolHandler';
import { MultiImageToolHandler } from './MultiImageToolHandler';
import { MemoryToolHandler } from './MemoryToolHandler';
import { SwitchModeToolHandler } from './SwitchModeToolHandler';
// Import BaseToolHandler and ToolHandlerRegistry if they are not already implicitly available
// (though BaseToolHandler handles its own registration)

// Define your event types here for better type safety
// Example:
// type Events = {
//   'artifact:open': { view: string; data?: any };
//   'chat:update-segment': { segmentId: string; content: string; status?: string };
//   'brief:streaming': { placeholderId: string; content: string };
//   'brief:complete': { placeholderId: string; finalContent: string };
//   'email:streaming': { placeholderId: string; content: string };
//   'email:complete': { placeholderId: string; finalContent: string };
//   'plan:streaming': { placeholderId: string; content: string };
//   'plan:complete': { placeholderId: string; finalContent: string };
//   // Add other tool-specific events as they are defined
// };
// const emitter: Emitter<Events> = mitt<Events>();

const emitter: Emitter<any> = mitt(); // Using 'any' for now, refine with specific event types later.

export function initializeToolHandlers() {
  // Initialize all tool handlers.
  // The BaseToolHandler constructor handles registration with the ToolHandlerRegistry.
  new BriefToolHandler(emitter);
  new EmailToolHandler(emitter);
  new PlanToolHandler(emitter);
  new AnalyticsToolHandler(emitter);
  new ImageToolHandler(emitter);
  new ImageEditToolHandler(emitter);
  new ImageTextEditToolHandler(emitter);
  new MultiImageToolHandler(emitter);
  new MemoryToolHandler(emitter);
  new SwitchModeToolHandler(emitter);

  console.log('All tool handlers initialized and registered, including Image, ImageEdit, ImageTextEdit, MultiImage, Memory, and SwitchMode.');
  return emitter;
}

// Export the emitter for components to use
export const toolEvents = emitter;
