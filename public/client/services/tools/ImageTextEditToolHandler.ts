import { BaseTool<PERSON>and<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';

export class ImageTextEditToolHandler extends BaseToolHandler {
  public readonly tag = 'image_text_edit'; // Unique tag for this tool

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    // No initial chat segment is created by this handler directly.
    // The segment will be created when onEnd is called with the text edit data.
    console.log(`${this.tag} tool started. PlaceholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    // Image text edit content is typically not streamed character by character.
    // It's usually a single block of JSON containing the result.
    // We'll accumulate it here and process fully in onEnd.
    this.streamingContent += contentChunk;
    console.log(`${this.tag} tool received content chunk. Current accumulated: ${this.streamingContent.substring(0,50)}...`);
  }

  onEnd(): void {
    if (!this.placeholderId) {
      console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    const rawContent = this.streamingContent.trim();

    try {
      if (rawContent.startsWith('{') && rawContent.endsWith('}')) {
        const textEditData = JSON.parse(rawContent);

        // Emit a chat segment for the image text edit tool
        this.emitter.emit('chat:update-segment', {
          type: 'image_text_edit',
          sender: 'ai',
          imageUrl: textEditData.imageUrl || '',
          text: textEditData.text || '',
          x: textEditData.x || 0,
          y: textEditData.y || 0,
          font: textEditData.font || 'Arial',
          fontSize: textEditData.fontSize || 16,
          color: textEditData.color || '#000000',
          timestamp: new Date(),
        });

        console.log(`${this.tag} tool ended. Successfully processed image text edit data.`);
      } else {
        // Invalid response format
        console.error(`${this.tag} tool: Invalid response format:`, rawContent);
        this.emitter.emit('chat:update-segment', {
          type: 'text',
          sender: 'ai',
          content: `[Error: Invalid response format from image text edit tool]`,
          timestamp: new Date(),
        });
      }
    } catch (e) {
      console.error(`${this.tag} tool: Error parsing content:`, e, "Raw content:", rawContent);
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `[Error: Failed to process image text edit result]`,
        timestamp: new Date(),
      });
    }

    this.emitter.emit(`${this.tag}:complete`, {
      placeholderId: this.placeholderId,
      finalContent: rawContent
    });

    this.placeholderId = null;
    this.streamingContent = '';
  }
}
